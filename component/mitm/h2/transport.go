// copy and modify from "golang.org/x/net/http2"

// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Transport code.

package h2

import (
	"crypto/tls"
	"net"
	"net/http"
	"reflect"
	_ "unsafe"

	"golang.org/x/net/http2"
)

const nextProtoUnencryptedHTTP2 = "unencrypted_http2"

//go:linkname unencryptedNetConnFromTLSConn golang.org/x/net/http2.unencryptedNetConnFromTLSConn
func unencryptedNetConnFromTLSConn(tc *tls.Conn) (net.Conn, error)

//go:linkname authorityAddr golang.org/x/net/http2.authorityAddr
func authorityAddr(scheme string, authority string) (addr string)

var roundTripOptAllowHTTP http2.RoundTripOpt

func init() {
	field, found := reflect.TypeOf(http2.RoundTripOpt{}).FieldByName("allowHTTP")
	if !found {
		panic("http2.RoundTripOpt doesn't have the field allowHTTP")
	}
	if field.Type.Kind() != reflect.Bool {
		panic("http2.RoundTripOpt's field allowHTTP is not a bool")
	}
	reflect.ValueOf(&roundTripOptAllowHTTP).Elem().FieldByName("allowHTTP").SetBool(true)
}

type erringRoundTripper struct{ err error }

func (rt erringRoundTripper) RoundTripErr() error                             { return rt.err }
func (rt erringRoundTripper) RoundTrip(*http.Request) (*http.Response, error) { return nil, rt.err }

// unencryptedTransport is a Transport with a RoundTrip method that
// always permits http:// URLs.
type unencryptedTransport http2.Transport

func (t *unencryptedTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	return (*http2.Transport)(t).RoundTripOpt(req, roundTripOptAllowHTTP)
}

// ConfigureTransport configures a net/http HTTP/1 Transport to use HTTP/2.
// It returns an error if t1 has already been HTTP/2-enabled.
//
// Use ConfigureTransports instead to configure the HTTP/2 Transport.
func ConfigureTransport(t1 *http.Transport) error {
	_, err := ConfigureTransports(t1)
	return err
}

// ConfigureTransports configures a net/http HTTP/1 Transport to use HTTP/2.
// It returns a new HTTP/2 Transport for further configuration.
// It returns an error if t1 has already been HTTP/2-enabled.
func ConfigureTransports(t1 *http.Transport) (*http2.Transport, error) {
	return configureTransports(t1)
}

func configureTransports(t1 *http.Transport) (*http2.Transport, error) {
	t2, err := http2.ConfigureTransports(t1)
	if err != nil {
		return nil, err
	}
	connPool := new(clientConnPool)
	connPool.t = t2
	t2.ConnPool = connPool
	upgradeFn := func(scheme, authority string, c net.Conn) http.RoundTripper {
		addr := authorityAddr(scheme, authority)
		if used, err := connPool.addConnIfNeeded(addr, t2, c); err != nil {
			go c.Close()
			return erringRoundTripper{err}
		} else if !used {
			// Turns out we don't need this c.
			// For example, two goroutines made requests to the same host
			// at the same time, both kicking off TCP dials. (since protocol
			// was unknown)
			go c.Close()
		}
		if scheme == "http" {
			return (*unencryptedTransport)(t2)
		}
		return t2
	}
	t1.TLSNextProto[http2.NextProtoTLS] = func(authority string, c *tls.Conn) http.RoundTripper {
		return upgradeFn("https", authority, c)
	}
	// The "unencrypted_http2" TLSNextProto key is used to pass off non-TLS HTTP/2 conns.
	t1.TLSNextProto[nextProtoUnencryptedHTTP2] = func(authority string, c *tls.Conn) http.RoundTripper {
		nc, err := unencryptedNetConnFromTLSConn(c)
		if err != nil {
			go c.Close()
			return erringRoundTripper{err}
		}
		return upgradeFn("http", authority, nc)
	}
	return t2, nil
}

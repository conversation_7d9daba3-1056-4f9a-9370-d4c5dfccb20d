// copy and modify from "golang.org/x/net/http2"

// Copyright 2015 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Transport code's client connection pooling.

package h2

import (
	"context"
	"crypto/tls"
	"errors"
	"fmt"
	"net"
	"net/http"
	"reflect"
	"strings"
	"unsafe"
	_ "unsafe"

	"github.com/metacubex/mihomo/common/singleflight"
	"github.com/metacubex/mihomo/common/xsync"
	"github.com/metacubex/mihomo/component/mitm/h2/utils"
	"golang.org/x/net/http2"
)

//go:linkname clientConnCloseIfIdle golang.org/x/net/http2.(*ClientConn).closeIfIdle
func clientConnCloseIfIdle(cc *http2.ClientConn)

//go:linkname isConnectionCloseRequest golang.org/x/net/http2.isConnectionCloseRequest
func isConnectionCloseRequest(req *http.Request) bool

//go:linkname transportDialClientConn golang.org/x/net/http2.(*Transport).dialClientConn
func transportDialClientConn(t *http2.Transport, ctx context.Context, addr string, singleUse bool) (*http2.ClientConn, error)

type partialClientConn struct {
	t        *http2.Transport
	tconn    net.Conn
	tlsState *tls.ConnectionState
}

func init() {
	ccType := reflect.TypeOf(http2.ClientConn{})
	pccType := reflect.TypeOf(partialClientConn{})
	for i := 0; i < pccType.NumField(); i++ {
		ccField := ccType.Field(i)
		pccField := pccType.Field(i)
		if ccField.Offset != pccField.Offset {
			panic(fmt.Sprintf("http2.ClientConn field offset mismatch for field %d (%s): original %d, partial %d.", i, ccField.Name, ccField.Offset, pccField.Offset))
		}
		if ccField.Type != pccField.Type {
			panic(fmt.Sprintf("http2.ClientConn field type mismatch for field %d (%s): original %s, partial %s.", i, ccField.Name, ccField.Type, pccField.Type))
		}
		if ccField.Name != pccField.Name {
			panic(fmt.Sprintf("http2.ClientConn field name mismatch for field %d: original %s, partial %s.", i, ccField.Name, pccField.Name))
		}
	}
}

func clientConnGetTLSState(cc *http2.ClientConn) *tls.ConnectionState {
	return (*partialClientConn)(unsafe.Pointer(cc)).tlsState
}

// incomparable is a zero-width, non-comparable type. Adding it to a struct
// makes that struct also non-comparable, and generally doesn't add
// any size (as long as it's first).
type incomparable [0]func()

// ClientConnPool manages a pool of HTTP/2 client connections.
type ClientConnPool interface {
	// GetClientConn returns a specific HTTP/2 connection (usually
	// a TLS-TCP connection) to an HTTP/2 server. On success, the
	// returned ClientConn accounts for the upcoming RoundTrip
	// call, so the caller should not omit it. If the caller needs
	// to, ClientConn.RoundTrip can be called with a bogus
	// new(http.Request) to release the stream reservation.
	GetClientConn(req *http.Request, addr string) (*http2.ClientConn, error)
	MarkDead(*http2.ClientConn)
}

// clientConnPoolIdleCloser is the interface implemented by ClientConnPool
// implementations which can close their idle connections.
type clientConnPoolIdleCloser interface {
	ClientConnPool
	closeIdleConnections()
}

var (
	_ clientConnPoolIdleCloser = (*clientConnPool)(nil)
	_ clientConnPoolIdleCloser = noDialClientConnPool{}
)

type void struct{}
type keySet map[string]void
type connSet utils.ConcurrentSet[*http2.ClientConn]

type clientConnPool struct {
	t *http2.Transport

	conns        xsync.Map[string, connSet] // key is host:port
	keys         xsync.Map[*http2.ClientConn, keySet]
	dialing      singleflight.Group[string, *dialCall]       // currently in-flight dials
	addConnCalls singleflight.Group[string, void]            // in-flight addConnIfNeeded calls
	shutdowns    singleflight.Group[*http2.ClientConn, void] // in-flight shutdowns
}

func (p *clientConnPool) GetClientConn(req *http.Request, addr string) (*http2.ClientConn, error) {
	return p.getClientConn(req, addr, dialOnMiss)
}

const (
	dialOnMiss   = true
	noDialOnMiss = false
)

func (p *clientConnPool) allConns(key string) func(yield func(*http2.ClientConn) bool) {
	s, ok := p.conns.Load(key)
	if !ok {
		return nil
	}
	return s.ForEachIf
}

func getWildcardFrom(addr string) (string, bool) {
	host, port, err := net.SplitHostPort(addr)
	if err != nil {
		host = addr
		port = ""
	}
	if net.ParseIP(host) != nil {
		return "", false
	}
	parts := strings.Split(host, ".")
	if len(parts) <= 2 {
		return "", false
	}
	if len(port) != 0 {
		port = ":" + port
	}
	return "*." + strings.Join(parts[1:], ".") + port, true
}

func (p *clientConnPool) getClientConn(req *http.Request, addr string, dialOnMiss bool) (*http2.ClientConn, error) {
	// TODO(dneil): Dial a new connection when t.DisableKeepAlives is set?
	if isConnectionCloseRequest(req) && dialOnMiss {
		// It gets its own connection.
		cc, err := transportDialClientConn(p.t, req.Context(), addr, true)
		if err != nil {
			return nil, err
		}
		return cc, nil
	}
	wc, ok := getWildcardFrom(addr)
	for {
		for cc := range p.allConns(addr) {
			if cc.ReserveNewRequest() {
				return cc, nil
			}
		}
		if ok {
			for cc := range p.allConns(wc) {
				if cc.ReserveNewRequest() {
					return cc, nil
				}
			}
		}
		if !dialOnMiss {
			return nil, http2.ErrNoCachedConn
		}
		call, err := p.getStartDialLocked(req.Context(), addr)
		if shouldRetryDial(call, err, req) {
			continue
		}
		cc := call.res
		if err != nil {
			return nil, err
		}
		if cc.ReserveNewRequest() {
			return cc, nil
		}
	}
}

// dialCall is an in-flight Transport dial call to a host.
type dialCall struct {
	_ incomparable
	// the context associated with the request
	// that created this dialCall
	ctx context.Context
	res *http2.ClientConn
}

func getKeysFrom(addr string, cc *http2.ClientConn) (keys []string) {
	tlsState := clientConnGetTLSState(cc)
	if tlsState != nil {
		_, port, err := net.SplitHostPort(addr)
		if err != nil {
			return []string{addr}
		}
		set := make(keySet)
		set[addr] = void{}
		for _, cert := range tlsState.PeerCertificates {
			if cert != nil {
				for _, dnsName := range cert.DNSNames {
					set[net.JoinHostPort(dnsName, port)] = void{}
				}
			}
		}
		for k := range set {
			keys = append(keys, k)
		}
		return keys
	} else {
		return []string{addr}
	}
}

func (p *clientConnPool) getStartDialLocked(ctx context.Context, addr string) (*dialCall, error) {
	call, err, _ := p.dialing.Do(addr, func() (*dialCall, error) {
		ret := &dialCall{ctx: ctx, res: nil}
		cc, err := transportDialClientConn(p.t, ctx, addr, false)
		if err != nil {
			return ret, err
		}
		p.addConnLocked(cc, getKeysFrom(addr, cc)...)
		ret.res = cc
		return ret, err
	})
	return call, err
}

// addConnIfNeeded makes a NewClientConn out of c if a connection for key doesn't
// already exist. It coalesces concurrent calls with the same key.
// This is used by the http1 Transport code when it creates a new connection. Because
// the http1 Transport doesn't de-dup TCP dials to outbound hosts (because it doesn't know
// the protocol), it can get into a situation where it has multiple TLS connections.
// This code decides which ones live or die.
// The return value used is whether c was used.
// c is never closed.
func (p *clientConnPool) addConnIfNeeded(key string, t *http2.Transport, c net.Conn) (used bool, err error) {
	for cc := range p.allConns(key) {
		if cc.CanTakeNewRequest() {
			return false, nil
		}
	}
	wc, ok := getWildcardFrom(key)
	if ok {
		for cc := range p.allConns(wc) {
			if cc.CanTakeNewRequest() {
				return false, nil
			}
		}
	}

	used = false
	_, err, _ = p.addConnCalls.Do(key, func() (void, error) {
		cc, err := t.NewClientConn(c)
		if err == nil {
			used = true
			p.addConnLocked(cc, getKeysFrom(key, cc)...)
		}
		return void{}, err
	})

	return used, err
}

func (p *clientConnPool) addConnLocked(cc *http2.ClientConn, keys ...string) {
	p.keys.Compute(cc, func(oldValue keySet, loaded bool) (newValue keySet, op xsync.ComputeOp) {
		if !loaded {
			newValue = make(keySet)
		} else {
			newValue = oldValue
		}
		for _, key := range keys {
			if _, ok := newValue[key]; ok {
				continue
			}
			newValue[key] = void{}
			p.conns.Compute(key, func(oldValue connSet, loaded bool) (newValue connSet, op xsync.ComputeOp) {
				if !loaded {
					newValue = utils.NewConcurrentSet[*http2.ClientConn]()
					newValue.Insert(cc)
					return newValue, xsync.UpdateOp
				}
				oldValue.Insert(cc)
				return oldValue, xsync.CancelOp
			})
		}
		if loaded {
			return oldValue, xsync.CancelOp
		}
		return newValue, xsync.UpdateOp
	})
}

func (p *clientConnPool) MarkDead(cc *http2.ClientConn) {
	p.keys.Compute(cc, func(oldValue keySet, loaded bool) (keySet, xsync.ComputeOp) {
		if !loaded {
			return oldValue, xsync.CancelOp
		}
		for key := range oldValue {
			if conns, ok := p.conns.Load(key); ok && conns.Remove(cc) && conns.IsEmpty() {
				p.conns.Compute(key, func(oldValue connSet, loaded bool) (connSet, xsync.ComputeOp) {
					if loaded && oldValue.IsEmpty() {
						return nil, xsync.DeleteOp
					}
					return oldValue, xsync.CancelOp
				})
			}
		}
		return nil, xsync.DeleteOp
	})
}

//func (p *clientConnPool) closeIfIdle(cc *http2.ClientConn) {
//	s1 := cc.State()
//	if s1.StreamsActive > 0 || s1.StreamsReserved > 0 {
//		return
//	}
//	if !s1.LastIdle.IsZero() {
//		p.MarkDead(cc)
//	}
//	p.shutdowns.Go(cc, func() (void, error) {
//		if s1.LastIdle.IsZero() {
//			time.Sleep(time.Second)
//			if s2 := cc.State(); s2.StreamsActive > 0 || s2.StreamsReserved > 0 {
//				return void{}, nil
//			}
//			p.MarkDead(cc)
//		}
//		err := cc.Shutdown(context.Background())
//		if err != nil {
//			cc.Close()
//		}
//		return void{}, err
//	})
//}

func (p *clientConnPool) closeIdleConnections() {
	// TODO: don't close a cc if it was just added to the pool
	// milliseconds ago and has never been used. There's currently
	// a small race window with the HTTP/1 Transport's integration
	// where it can add an idle conn just before using it, and
	// somebody else can concurrently call CloseIdleConns and
	// break some caller's RoundTrip.
	p.conns.Range(func(key string, conns connSet) bool {
		for cc := range conns.ForEachIf {
			clientConnCloseIfIdle(cc)
		}
		return true
	})
}

// noDialClientConnPool is an implementation of http2.ClientConnPool
// which never dials. We let the HTTP/1.1 client dial and use its TLS
// connection instead.
type noDialClientConnPool struct{ *clientConnPool }

func (p noDialClientConnPool) GetClientConn(req *http.Request, addr string) (*http2.ClientConn, error) {
	return p.getClientConn(req, addr, noDialOnMiss)
}

// shouldRetryDial reports whether the current request should
// retry dialing after the call finished unsuccessfully, for example
// if the dial was canceled because of a context cancellation or
// deadline expiry.
func shouldRetryDial(call *dialCall, err error, req *http.Request) bool {
	if err == nil {
		// No error, no need to retry
		return false
	}
	if call.ctx == req.Context() {
		// If the call has the same context as the request, the dial
		// should not be retried, since any cancellation will have come
		// from this request.
		return false
	}
	if !errors.Is(err, context.Canceled) && !errors.Is(err, context.DeadlineExceeded) {
		// If the call error is not because of a context cancellation or a deadline expiry,
		// the dial should not be retried.
		return false
	}
	// Only retry if the error is a context cancellation error or deadline expiry
	// and the context associated with the call was canceled or expired.
	return call.ctx.Err() != nil
}

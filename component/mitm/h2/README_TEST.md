# HTTP/2 Client Connection Pool Tests

This directory contains comprehensive tests for the HTTP/2 client connection pool implementation.

## Files

- `client_conn_pool.go` - Main implementation (modified from golang.org/x/net/http2)
- `client_conn_pool_test.go` - Comprehensive test suite
- `transport.go` - Transport configuration utilities
- `utils/` - Utility functions and concurrent data structures

## Test Coverage

The test suite covers:

### Core Functionality
- `clientConnPool.GetClientConn()` - Connection retrieval and pooling
- `clientConnPool.MarkDead()` - Connection cleanup
- `clientConnPool.addConnIfNeeded()` - Connection addition logic
- `clientConnPool.closeIdleConnections()` - Idle connection cleanup

### Transport Integration
- `ConfigureTransports()` - HTTP/1 to HTTP/2 transport configuration
- `ConfigureTransport()` - Single transport configuration
- Integration with `transport.go` functionality

### Utility Functions
- `getWildcardFrom()` - Wildcard domain extraction
- `getKeysFrom()` - TLS certificate-based key extraction
- `shouldRetryDial()` - Dial retry logic

### Concurrent Operations
- Thread-safe connection pool operations
- Concurrent dial handling
- Race condition prevention

### Edge Cases
- No-dial connection pool (`noDialClientConnPool`)
- Error handling and recovery
- Connection state management

## Running Tests

```bash
# Run all tests
go test ./component/mitm/h2/

# Run with verbose output
go test -v ./component/mitm/h2/

# Run specific test
go test -run TestClientConnPool_GetClientConn ./component/mitm/h2/

# Run benchmarks
go test -bench=. ./component/mitm/h2/

# Run with race detection
go test -race ./component/mitm/h2/
```

## Test Environment Notes

Some tests may fail in isolated test environments due to:
- Network connectivity requirements
- TLS certificate validation
- HTTP/2 protocol negotiation

These failures are expected and logged appropriately. The tests focus on:
1. Interface compliance
2. Code path coverage
3. Concurrent safety
4. Error handling

## Dependencies

The tests use:
- `github.com/stretchr/testify` for assertions
- `golang.org/x/net/http2` for HTTP/2 functionality
- Standard library `net/http/httptest` for test servers

## Integration with mihomo

This connection pool integrates with mihomo's MITM functionality to provide:
- Efficient HTTP/2 connection reuse
- Certificate-based connection sharing
- Wildcard domain support
- Concurrent connection management

The implementation is based on golang.org/x/net/http2 but modified for mihomo's specific requirements.

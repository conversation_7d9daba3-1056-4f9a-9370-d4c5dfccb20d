package utils

import (
	"strconv"
	"sync"
	"sync/atomic"
	"testing"
)

// Benchmark for a single key lock/unlock cycle.
func BenchmarkNamedLocker_LockUnlock(b *testing.B) {
	locker := NewNamedLocker[string]()
	key := "test_key"
	b.<PERSON>setTimer()
	for i := 0; i < b.N; i++ {
		locker.Lock(key)
		locker.Unlock(key)
	}
}

// Benchmark for a single key RLock/RUnlock cycle.
func BenchmarkNamedLocker_RLockRUnlock(b *testing.B) {
	locker := NewNamedLocker[string]()
	key := "test_key"
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		locker.RLock(key)
		locker.RUnlock(key)
	}
}

// Benchmark for concurrent access on the same key, causing high contention.
func BenchmarkNamedLocker_ConcurrentAccessSameKey(b *testing.B) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	b.ResetTimer()
	b.<PERSON>(func(pb *testing.PB) {
		for pb.Next() {
			locker.Lock(key)
			locker.Unlock(key)
		}
	})
}

// Benchmark for concurrent read access on the same key.
func BenchmarkNamedLocker_ConcurrentReadSameKey(b *testing.B) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			locker.RLock(key)
			locker.RUnlock(key)
		}
	})
}

// Benchmark for concurrent mixed read/write access on the same key.
func BenchmarkNamedLocker_ConcurrentMixedAccessSameKey(b *testing.B) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		// 90% reads, 10% writes
		var i int
		for pb.Next() {
			if i%10 == 0 {
				locker.Lock(key)
				locker.Unlock(key)
			} else {
				locker.RLock(key)
				locker.RUnlock(key)
			}
			i++
		}
	})
}

// Benchmark for concurrent access on different integer keys, causing low contention.
func BenchmarkNamedLocker_ConcurrentAccessDifferentKeys(b *testing.B) {
	locker := NewNamedLocker[int]()
	var counter int64
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := int(atomic.AddInt64(&counter, 1))
			locker.Lock(key)
			locker.Unlock(key)
		}
	})
}

// Benchmark for concurrent read access on different integer keys, causing low contention.
func BenchmarkNamedLocker_ConcurrentReadDifferentKeys(b *testing.B) {
	locker := NewNamedLocker[int]()
	var counter int64
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := int(atomic.AddInt64(&counter, 1))
			locker.RLock(key)
			locker.RUnlock(key)
		}
	})
}

// Benchmark for concurrent access on different string keys, causing low contention.
func BenchmarkNamedLocker_ConcurrentAccessDifferentStringKeys(b *testing.B) {
	locker := NewNamedLocker[string]()
	var counter int64
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := strconv.FormatInt(atomic.AddInt64(&counter, 1), 10)
			locker.Lock(key)
			locker.Unlock(key)
		}
	})
}

// Benchmark for concurrent read access on different string keys, causing low contention.
func BenchmarkNamedLocker_ConcurrentReadDifferentStringKeys(b *testing.B) {
	locker := NewNamedLocker[string]()
	var counter int64
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			key := strconv.FormatInt(atomic.AddInt64(&counter, 1), 10)
			locker.RLock(key)
			locker.RUnlock(key)
		}
	})
}

// --- Baseline benchmarks for comparison ---

// Baseline for a standard Mutex Lock/Unlock cycle.
func BenchmarkMutex_LockUnlock(b *testing.B) {
	var mu sync.Mutex
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mu.Lock()
		mu.Unlock()
	}
}

// Baseline for concurrent access on a single standard Mutex.
func BenchmarkMutex_Concurrent(b *testing.B) {
	var mu sync.Mutex
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			mu.Lock()
			mu.Unlock()
		}
	})
}

// Baseline for a standard RWMutex Lock/Unlock cycle.
func BenchmarkRWMutex_LockUnlock(b *testing.B) {
	var mu sync.RWMutex
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mu.Lock()
		mu.Unlock()
	}
}

// Baseline for a standard RWMutex RLock/RUnlock cycle.
func BenchmarkRWMutex_RLockRUnlock(b *testing.B) {
	var mu sync.RWMutex
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		mu.RLock()
		mu.RUnlock()
	}
}

// Baseline for concurrent write-locking on a single standard RWMutex.
func BenchmarkRWMutex_ConcurrentLock(b *testing.B) {
	var mu sync.RWMutex
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			mu.Lock()
			mu.Unlock()
		}
	})
}

// Baseline for concurrent read-locking on a single standard RWMutex.
func BenchmarkRWMutex_ConcurrentRLock(b *testing.B) {
	var mu sync.RWMutex
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		for pb.Next() {
			mu.RLock()
			mu.RUnlock()
		}
	})
}

// Baseline for concurrent mixed read/write access on a single standard RWMutex.
func BenchmarkRWMutex_ConcurrentMixed(b *testing.B) {
	var mu sync.RWMutex
	b.ResetTimer()
	b.RunParallel(func(pb *testing.PB) {
		var i int
		for pb.Next() {
			if i%10 == 0 {
				mu.Lock()
				mu.Unlock()
			} else {
				mu.RLock()
				mu.RUnlock()
			}
			i++
		}
	})
}
package utils

import (
	"sync"
	"testing"
	"time"
)

func TestNamedRWLock_SingleKeyLockUnlock(t *testing.T) {
	locker := NewNamedLocker[string]()
	key := "test_key"

	locker.Lock(key)
	locker.Unlock(key)
}

func TestNamedRWLock_SingleKeyRLockRUnlock(t *testing.T) {
	locker := NewNamedLocker[string]()
	key := "test_key"

	locker.RLock(key)
	locker.RUnlock(key)
}

func TestNamedRWLock_UnlockPanicOnUnusedKey(t *testing.T) {
	locker := NewNamedLocker[string]()

	defer func() {
		if r := recover(); r == nil {
			t.<PERSON><PERSON><PERSON>("Unlock on a non-existent key should have panicked")
		}
	}()
	locker.Unlock("non_existent_key")
}

func TestNamedRWLock_RUnlockPanicOnUnusedKey(t *testing.T) {
	locker := NewNamedLocker[string]()

	defer func() {
		if r := recover(); r == nil {
			t.<PERSON><PERSON><PERSON>("RUnlock on a non-existent key should have panicked")
		}
	}()
	locker.RUnlock("non_existent_key")
}

func TestNamedRWLock_ConcurrentWriteAccessSameKey(t *testing.T) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	counter := 0
	numGoroutines := 100
	var wg sync.WaitGroup

	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			locker.Lock(key)
			counter++
			locker.Unlock(key)
		}()
	}

	wg.Wait()

	if counter != numGoroutines {
		t.Errorf("Expected counter to be %d, but got %d", numGoroutines, counter)
	}
}

func TestNamedRWLock_ConcurrentReadAccessSameKey(t *testing.T) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	c := make(chan bool)

	locker.RLock(key)
	go func() {
		locker.RLock(key)
		locker.RUnlock(key)
		c <- true
	}()

	select {
	case <-c:
	// Success, the second RLock was not blocked
	case <-time.After(50 * time.Millisecond):
		t.Error("Second RLock was blocked")
	}
	locker.RUnlock(key)
}

func TestNamedRWLock_ConcurrentAccessDifferentKeys(t *testing.T) {
	locker := NewNamedLocker[int]()
	numGoroutines := 100
	var wg sync.WaitGroup

	wg.Add(numGoroutines)
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			key := id
			if id%2 == 0 {
				locker.Lock(key)
				locker.Unlock(key)
			} else {
				locker.RLock(key)
				locker.RUnlock(key)
			}
		}(i)
	}

	wg.Wait()
}

func TestNamedRWLock_ReadBlocksWrite(t *testing.T) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	c := make(chan bool)

	locker.RLock(key)

	go func() {
		locker.Lock(key)
		locker.Unlock(key)
		c <- true
	}()

	select {
	case <-c:
		t.Fatal("Lock should be blocked by RLock")
	case <-time.After(50 * time.Millisecond):
		// Correctly blocked
	}

	locker.RUnlock(key)

	select {
	case <-c:
	// Correctly unblocked
	case <-time.After(50 * time.Millisecond):
		t.Fatal("Lock should have been unblocked by RUnlock")
	}
}

func TestNamedRWLock_WriteBlocksRead(t *testing.T) {
	locker := NewNamedLocker[string]()
	key := "shared_key"
	c := make(chan bool)

	locker.Lock(key)

	go func() {
		locker.RLock(key)
		locker.RUnlock(key)
		c <- true
	}()

	select {
	case <-c:
		t.Fatal("RLock should be blocked by Lock")
	case <-time.After(50 * time.Millisecond):
		// Correctly blocked
	}

	locker.Unlock(key)

	select {
	case <-c:
	// Correctly unblocked
	case <-time.After(50 * time.Millisecond):
		t.Fatal("RLock should have been unblocked by Unlock")
	}
}
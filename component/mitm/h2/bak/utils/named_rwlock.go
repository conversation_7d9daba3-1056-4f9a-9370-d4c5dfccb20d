package utils

import (
	"fmt"
	"sync"

	"github.com/metacubex/mihomo/common/xsync"
)

type refLocker struct {
	sync.RWMutex
	ref int
}

type NamedRWLock[K comparable] struct {
	m *xsync.Map[K, *refLocker]
}

func NewNamedLocker[K comparable]() *NamedRWLock[K] {
	return &NamedRWLock[K]{
		m: xsync.NewMap[K, *refLocker](),
	}
}

func (nl *NamedRWLock[K]) getLocker(key K) *refLocker {
	rl, _ := nl.m.Compute(key, func(oldValue *refLocker, loaded bool) (*refLocker, xsync.ComputeOp) {
		if loaded {
			oldValue.ref++
			return oldValue, xsync.UpdateOp
		}
		return &refLocker{ref: 1}, xsync.UpdateOp
	})
	return rl
}

func (nl *NamedRWLock[K]) releaseLocker(key K, opName string, unlockFunc func(*refLocker)) {
	nl.m.Compute(key, func(oldValue *refLocker, loaded bool) (*refLocker, xsync.ComputeOp) {
		if !loaded {
			panic(fmt.Sprintf("NamedRWLock: %s of a non-existent or already-unlocked key: %v", opName, key))
		}
		unlockFunc(oldValue)
		oldValue.ref--
		if oldValue.ref == 0 {
			return nil, xsync.DeleteOp
		}
		return oldValue, xsync.UpdateOp
	})
}

func (nl *NamedRWLock[K]) Lock(key K) {
	nl.getLocker(key).Lock()
}

func (nl *NamedRWLock[K]) Unlock(key K) {
	nl.releaseLocker(key, "Unlock", (*refLocker).Unlock)
}

func (nl *NamedRWLock[K]) RLock(key K) {
	nl.getLocker(key).RLock()
}

func (nl *NamedRWLock[K]) RUnlock(key K) {
	nl.releaseLocker(key, "RUnlock", (*refLocker).RUnlock)
}

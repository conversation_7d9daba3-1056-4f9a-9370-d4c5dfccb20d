package utils

import "github.com/metacubex/mihomo/common/xsync"

type container interface {
	IsEmpty() bool
	Len() int
	Clear()
}

// ConcurrentSet is a containers that store unique elements.
type ConcurrentSet[K any] interface {
	container
	Has(K) bool
	HasOrInsert(K) bool
	Insert(K)
	InsertN(...K)
	Remove(K) bool
	RemoveN(...K)
	ForEach(func(K))
	ForEachIf(func(K) bool)
}

// xSet is a concurrent set implemented using a Map.
// It is safe for concurrent use by multiple goroutines.
type xSet[K comparable] struct {
	xsync.Map[K, struct{}]
}

// NewConcurrentSet creates a new concurrent set.
func NewConcurrentSet[K comparable]() ConcurrentSet[K] {
	return new(xSet[K])
}

// IsEmpty checks if the set has no elements.
func (s *xSet[K]) IsEmpty() bool {
	return s.Len() == 0
}

// Len returns the number of elements in the set.
func (s *xSet[K]) Len() int {
	return s.Size()
}

// Clear erases all elements from the set.
func (s *xSet[K]) Clear() {
	s.Map.Clear()
}

// Has checks whether the set contains the given key.
func (s *xSet[K]) Has(key K) bool {
	_, ok := s.Load(key)
	return ok
}

// HasOrInsert checks whether the set contains the given key, and inserts it if not.
// It returns true if the key was already in the set, false otherwise.
func (s *xSet[K]) HasOrInsert(key K) bool {
	_, loaded := s.LoadOrStore(key, struct{}{})
	return loaded
}

// Insert adds a key to the set.
func (s *xSet[K]) Insert(key K) {
	s.Store(key, struct{}{})
}

// InsertN adds multiple keys to the set.
func (s *xSet[K]) InsertN(keys ...K) {
	for _, key := range keys {
		s.Store(key, struct{}{})
	}
}

// Remove removes a key from the set.
// It returns true if the key was present in the set.
func (s *xSet[K]) Remove(key K) bool {
	_, loaded := s.LoadAndDelete(key)
	return loaded
}

// RemoveN removes multiple keys from the set.
func (s *xSet[K]) RemoveN(keys ...K) {
	for _, key := range keys {
		s.LoadAndDelete(key)
	}
}

// ForEach iterates over the elements of the set.
// The iteration order is not guaranteed.
func (s *xSet[K]) ForEach(f func(K)) {
	s.Range(func(key K, _ struct{}) bool {
		f(key)
		return true
	})
}

// ForEachIf iterates over the elements of the set, and stops when the callback returns false.
// The iteration order is not guaranteed.
func (s *xSet[K]) ForEachIf(f func(K) bool) {
	s.Range(func(key K, _ struct{}) bool {
		return f(key)
	})
}

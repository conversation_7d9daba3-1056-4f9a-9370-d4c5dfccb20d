package cert

import (
	"crypto/tls"
	"crypto/x509"
	"fmt"
	"io"
	"math/big"
	"math/rand"
	"net"
	"net/http"
	"net/http/httptest"
	"os"
	"reflect"
	"testing"
	"time"
)

func TestCreateCA(t *testing.T) {
	cert, key, err := createCA()
	if err != nil {
		t.Fatalf("Unexpected error: %v", err)
	}

	if cert == nil {
		t.Error("Certificate should not be nil")
	}

	if key == nil {
		t.Error("Private key should not be nil")
	}

	// Verify key size is 2048
	if key.N.BitLen() != 2048 {
		t.<PERSON><PERSON>("Expected key size 2048, got %d", key.N.BitLen())
	}

	// Verify certificate properties
	if !cert.IsCA {
		t.Error("Certificate should be marked as CA")
	}

	if cert.SignatureAlgorithm != x509.SHA256WithRSA {
		t.Error("Certificate should use SHA256WithRSA signature algorithm")
	}

	// Verify validity period
	now := time.Now()
	if cert.NotBefore.After(now.Add(time.Hour)) {
		t.Error("Certificate should be valid from an hour ago")
	}
	if cert.NotAfter.Before(now.Add(caValidity - time.Hour)) {
		t.Error("Certificate should be valid for the expected duration")
	}
}

func TestNewAuthority(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	if au == nil {
		t.Error("Authority should not be nil")
	}

	if au.caCert == nil {
		t.Error("CA certificate should not be nil")
	}

	if au.caPriv == nil {
		t.Error("CA private key should not be nil")
	}

	if au.storage == nil {
		t.Error("Storage should not be nil")
	}

	//if au.cache == nil {
	//	t.Error("Cache should not be nil")
	//}
	//
	//if au.group == nil {
	//	t.Error("Singleflight group should not be nil")
	//}

	// Test Certificate() method
	caCert := au.Certificate()
	if caCert == nil {
		t.Error("Certificate() should return CA certificate")
	}
	if !caCert.Equal(au.caCert) {
		t.Error("Certificate() should return the same CA certificate")
	}

	// Close first authority to release database lock
	au.Close()

	// Test loading existing CA
	au2, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to load existing authority: %v", err)
	}
	defer au2.Close()

	// Should have the same CA
	if !au.caCert.Equal(au2.caCert) {
		t.Error("Loaded CA should be the same as original")
	}
}

// TestConcurrentCertificateGenerationRaceCondition tests the race condition handling with singleflight
func TestConcurrentCertificateGenerationRaceCondition(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	domain := "race-test.example.com"
	numGoroutines := 10
	results := make(chan *tls.Certificate, numGoroutines)
	errors := make(chan error, numGoroutines)

	// Launch multiple goroutines to generate the same certificate concurrently
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			cert, err := au.Issue(fmt.Sprintf("test%d.%s", id, domain))
			if err != nil {
				errors <- fmt.Errorf("goroutine %d failed: %w", id, err)
				return
			}
			results <- cert
		}(i)
	}

	// Collect results
	var certs []*tls.Certificate
	for i := 0; i < numGoroutines; i++ {
		select {
		case cert := <-results:
			certs = append(certs, cert)
		case err := <-errors:
			t.Error(err)
		case <-time.After(time.Second * 10):
			t.Fatal("Test timed out")
		}
	}

	if len(certs) != numGoroutines {
		t.Fatalf("Expected %d certificates, got %d", numGoroutines, len(certs))
	}

	// All certificates should be valid and for the same domain
	for i, cert := range certs {
		if cert == nil {
			t.Errorf("Certificate %d is nil", i)
			continue
		}

		if cert.Leaf == nil {
			t.Errorf("Certificate %d has nil leaf", i)
			continue
		}

		// Check domain - use extractFromHost to get the expected common name
		expectedCommonName, _ := extractFromHost(domain, nil, nil)
		found := false
		for _, dnsName := range cert.Leaf.DNSNames {
			if dnsName == domain || dnsName == expectedCommonName {
				found = true
				break
			}
		}
		if !found && cert.Leaf.Subject.CommonName != expectedCommonName {
			t.Errorf("Certificate %d doesn't contain domain %s (expected CN: %s)", i, domain, expectedCommonName)
		}

		// Check validity
		now := time.Now()
		if now.After(cert.Leaf.NotAfter) || now.Before(cert.Leaf.NotBefore) {
			t.Errorf("Certificate %d is not valid at current time", i)
		}
	}

	// Verify that singleflight worked - all certificates should be the same instance
	firstCert := certs[0]
	for i := 1; i < len(certs); i++ {
		if certs[i] != firstCert {
			t.Errorf("Certificate %d is not the same instance, singleflight may not be working", i)
		}
	}

	// Verify certificate is properly cached
	expectedCommonName, _ := extractFromHost(domain, nil, nil)
	cachedCert, found := au.cache.Load(expectedCommonName)
	if !found {
		t.Error("Certificate should be cached")
	}

	if cachedCert != firstCert {
		t.Error("Cached certificate should be the same instance as returned certificates")
	}
}

// TestStorageCertificateOverwrite tests the storage overwrite behavior
func TestStorageCertificateOverwrite(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	domain := "overwrite-test.example.com"

	// Generate two different certificates for the same domain
	cert1, err := au.createCert(domain)
	if err != nil {
		t.Fatalf("Failed to generate first certificate: %v", err)
	}

	cert2, err := au.createCert(domain, cert1)
	if err != nil {
		t.Fatalf("Failed to generate second certificate: %v", err)
	}

	// Verify they are different
	if cert1.Leaf.SerialNumber.Cmp(cert2.Leaf.SerialNumber) == 0 {
		t.Fatal("Generated certificates should have different serial numbers")
	}

	// Verify DNSNames are the same
	if !reflect.DeepEqual(cert1.Leaf.DNSNames, cert2.Leaf.DNSNames) {
		t.Fatal("Generated certificates should have the same DNS names")
	}

	// Store first certificate
	err = au.storage.saveCert(cert1)
	if err != nil {
		t.Fatalf("Failed to store first certificate: %v", err)
	}

	// Store second certificate (should overwrite)
	err = au.storage.saveCert(cert2)
	if err != nil {
		t.Fatalf("Failed to store second certificate: %v", err)
	}

	// Verify that the database contains the second certificate
	expectedCommonName, _ := extractFromHost(domain, nil, nil)
	retrievedCert, err := au.storage.loadCert(expectedCommonName)
	if err != nil {
		t.Fatalf("Failed to retrieve certificate from storage: %v", err)
	}

	if retrievedCert.Leaf.SerialNumber.Cmp(cert2.Leaf.SerialNumber) != 0 {
		t.Error("Retrieved certificate should be the second certificate (overwritten)")
	}
}

func TestCertificateWithIPAddress(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Test with IP address
	ipStr := "***********"
	ip := net.ParseIP(ipStr)
	cert, err := au.Issue(ipStr)
	if err != nil {
		t.Fatalf("Failed to create certificate for IP: %v", err)
	}

	if cert.Leaf == nil {
		t.Error("Certificate leaf should not be nil")
	}

	if len(cert.Leaf.IPAddresses) == 0 {
		t.Error("Certificate should have IP addresses")
	}

	found := false
	for _, certIP := range cert.Leaf.IPAddresses {
		if certIP.Equal(ip) {
			found = true
			break
		}
	}

	if !found {
		t.Errorf("Certificate should contain the specified IP address %s. Got: %v", ipStr, cert.Leaf.IPAddresses)
	}

	// Verify common name is the IP address
	if cert.Leaf.Subject.CommonName != ipStr {
		t.Errorf("Expected common name %s, got %s", ipStr, cert.Leaf.Subject.CommonName)
	}
}

func TestCache(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	domain := "example.com"
	cert, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to create certificate: %v", err)
	}

	// Issue the certificate again - should be from cache
	cert2, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to get certificate from cache: %v", err)
	}

	// Should be the same certificate (from cache)
	if cert != cert2 {
		t.Error("Should return the same cached certificate")
	}

	// Test cache with different domain
	testDomain := "cache-test.example.com"
	testCert, err := au.Issue(testDomain)
	if err != nil {
		t.Fatalf("Failed to create test certificate: %v", err)
	}

	testCert2, err := au.Issue(testDomain)
	if err != nil {
		t.Fatalf("Failed to get cached test certificate: %v", err)
	}

	if testCert != testCert2 {
		t.Error("Should return the same cached certificate")
	}

	// Test cache key consistency
	expectedKey, _ := extractFromHost(testDomain, nil, nil)
	cachedCert, found := au.cache.Load(expectedKey)
	if !found {
		t.Error("Certificate should be cached with the expected key")
	}
	if cachedCert != testCert {
		t.Error("Cached certificate should be the same instance")
	}
}

// TestHTTPSServerWithDynamicCerts tests a complete HTTPS server scenario
func TestHTTPSServerWithDynamicCerts(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Create a test server with dynamic certificate generation
	server := httptest.NewUnstartedServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		fmt.Fprintf(w, "Hello from %s", r.Host)
	}))

	// Configure TLS with dynamic certificate generation
	server.TLS = au.NewTLSConfig()
	server.TLS.NextProtos = []string{"http/1.1"}
	server.StartTLS()
	defer server.Close()

	// Test multiple domains
	testDomains := []string{"example.com", "test.example.com", "api.example.com"}

	roots := x509.NewCertPool()
	roots.AddCert(au.Certificate())

	for _, domain := range testDomains {
		t.Run(domain, func(t *testing.T) {
			// Create a client that trusts our CA
			client := &http.Client{
				Transport: &http.Transport{
					TLSClientConfig: &tls.Config{
						RootCAs:    roots,
						ServerName: domain,
					},
				},
			}

			// Make request to the server
			req, err := http.NewRequest("GET", server.URL, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}
			req.Host = domain

			resp, err := client.Do(req)
			if err != nil {
				t.Fatalf("Failed to make request to %s: %v", domain, err)
			}
			defer resp.Body.Close()

			if resp.StatusCode != http.StatusOK {
				t.Errorf("Expected status 200, got %d", resp.StatusCode)
			}

			body, err := io.ReadAll(resp.Body)
			if err != nil {
				t.Fatalf("Failed to read response body: %v", err)
			}

			expected := fmt.Sprintf("Hello from %s", domain)
			if string(body) != expected {
				t.Errorf("Expected body %q, got %q", expected, string(body))
			}

			// Verify certificate was generated and cached
			expectedKey, _ := extractFromHost(domain, nil, nil)
			cert, found := au.cache.Load(expectedKey)
			if !found {
				t.Error("Certificate should be cached")
			}

			if cert.Leaf == nil {
				t.Error("Certificate leaf should not be nil")
			}

			// Check if domain is in certificate DNS names or common name
			found = false
			for _, dnsName := range cert.Leaf.DNSNames {
				if dnsName == domain || dnsName == expectedKey {
					found = true
					break
				}
			}
			if !found && cert.Leaf.Subject.CommonName != expectedKey {
				t.Errorf("Domain %s (expected key: %s) not found in certificate DNS names or CN", domain, expectedKey)
			}
		})
	}
}

// TestHTTP2ServerWithDynamicCerts tests HTTP/2 server with dynamic certificates
func TestHTTP2ServerWithDynamicCerts(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Create HTTP/2 server
	server := httptest.NewUnstartedServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		fmt.Fprintf(w, `{"host": "%s", "protocol": "%s"}`, r.Host, r.Proto)
	}))

	// Configure TLS for HTTP/2
	tlsConfig := au.NewTLSConfig()
	server.TLS = tlsConfig
	server.EnableHTTP2 = true
	tlsConfig.NextProtos = []string{"h2"}
	server.StartTLS()
	defer server.Close()

	testDomains := []string{"api.example.com", "cdn.example.com"}

	roots := x509.NewCertPool()
	roots.AddCert(au.Certificate())

	for _, domain := range testDomains {
		t.Run(domain, func(t *testing.T) {
			// Create client with proper TLS config for this domain
			domainClient := &http.Client{
				Transport: &http.Transport{
					TLSClientConfig: &tls.Config{
						RootCAs:    roots,
						ServerName: domain,
					},
					ForceAttemptHTTP2: true,
				},
			}

			req, err := http.NewRequest("GET", server.URL, nil)
			if err != nil {
				t.Fatalf("Failed to create request: %v", err)
			}
			req.Host = domain

			resp, err := domainClient.Do(req)
			if err != nil {
				t.Fatalf("Failed to make HTTP/2 request: %v", err)
			}
			defer resp.Body.Close()

			// Verify HTTP/2 was used
			if resp.ProtoMajor != 2 {
				t.Errorf("Expected HTTP/2, got HTTP/%d.%d", resp.ProtoMajor, resp.ProtoMinor)
			}

			body, err := io.ReadAll(resp.Body)
			if err != nil {
				t.Fatalf("Failed to read response body: %v", err)
			}

			expectedContent := fmt.Sprintf(`{"host": "%s", "protocol": "HTTP/2.0"}`, domain)
			if string(body) != expectedContent {
				t.Errorf("Expected body %q, got %q", expectedContent, string(body))
			}
		})
	}
}

// TestCertificateStorage tests certificate storage and retrieval
func TestCertificateStorage(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority with storage
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	testDomains := []string{"storage.example.com", "test.example.com"}

	serialNumber := big.NewInt(0)

	// Generate certificates
	for _, domain := range testDomains {
		cert, err := au.Issue(domain)
		if err != nil {
			t.Fatalf("Failed to create certificate for %s: %v", domain, err)
		}

		if cert.Leaf == nil {
			t.Errorf("Certificate leaf should not be nil for %s", domain)
		}

		// Verify domain is in certificate DNS names or common name
		expectedKey, _ := extractFromHost(domain, nil, nil)
		found := false
		for _, dnsName := range cert.Leaf.DNSNames {
			if dnsName == domain || dnsName == expectedKey {
				found = true
				break
			}
		}
		if !found && cert.Leaf.Subject.CommonName != expectedKey {
			t.Errorf("Domain %s (expected key: %s) not found in certificate DNS names or CN", domain, expectedKey)
		}
		serialNumber = cert.Leaf.SerialNumber
	}

	// Close first config to release database lock
	au.Close()

	// Create new authority with same storage to test persistence
	au2, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create second authority: %v", err)
	}
	defer au2.Close()

	// Verify certificates can be loaded from storage
	for _, domain := range testDomains {
		cert, err := au2.Issue(domain)
		if err != nil {
			t.Fatalf("Failed to get certificate for %s from storage: %v", domain, err)
		}

		if cert.Leaf == nil {
			t.Errorf("Loaded certificate leaf should not be nil for %s", domain)
		}

		// Verify the certificate contains the expected domain
		expectedKey, _ := extractFromHost(domain, nil, nil)
		found := false
		for _, dnsName := range cert.Leaf.DNSNames {
			if dnsName == domain || dnsName == expectedKey {
				found = true
				break
			}
		}
		if !found && cert.Leaf.Subject.CommonName != expectedKey {
			t.Errorf("Domain %s (expected key: %s) not found in loaded certificate", domain, expectedKey)
		}
		if cert.Leaf.SerialNumber.Cmp(serialNumber) != 0 {
			t.Errorf("Serial number mismatch for %s", domain)
		}
	}
}

// TestConcurrentCertificateGeneration tests concurrent certificate generation
func TestConcurrentCertificateGeneration(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create config with storage
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	const numGoroutines = 8
	const numDomains = 4

	results := make(chan error, numGoroutines*numDomains*2)

	// Launch multiple goroutines to generate certificates concurrently
	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			for j := 0; j < numDomains; j++ {
				go func() {
					time.Sleep(time.Duration(rand.Intn(200)) * time.Millisecond)
					domain := fmt.Sprintf("test%d.group%d.example.com", goroutineID, j)
					_, err := au.Issue(domain)
					if err != nil {
						results <- fmt.Errorf("goroutine %d failed to create cert for %s: %w", goroutineID, domain, err)
						return
					}
				}()

				domain := fmt.Sprintf("test%d.group%d.example.com", j, goroutineID)
				time.Sleep(time.Duration(rand.Intn(200)) * time.Millisecond)
				cert, err := au.Issue(domain)
				if err != nil {
					results <- fmt.Errorf("goroutine %d failed to create cert for %s: %w", goroutineID, domain, err)
					return
				}

				if cert.Leaf == nil {
					results <- fmt.Errorf("goroutine %d got nil leaf for %s", goroutineID, domain)
					return
				}

				// Verify certificate is cached
				expectedKey, _ := extractFromHost(domain, nil, nil)

				go func() {
					time.Sleep(time.Duration(rand.Intn(200)) * time.Millisecond)
					cert, found := au.cache.Load(expectedKey)
					if !found {
						results <- fmt.Errorf("certificate should be cached for %s", domain)
						return
					}
					if err = validate(cert, domain); err != nil {
						results <- fmt.Errorf("certificate should be valid: %s", err)
						return
					}
				}()
			}
			results <- nil
		}(i)
	}
	// Collect results
	for i := 0; i < numGoroutines; i++ {
		if err := <-results; err != nil {
			t.Error(err)
		}
	}
	cert, _ := au.cache.Load("example.com")
	if len(cert.Leaf.DNSNames) != numGoroutines+2 {
		t.Fatalf("Certificate should only have %d DNS name", numGoroutines+2)
	}
}

// TestIPAddressCertificates tests certificate generation for IP addresses
func TestIPAddressCertificates(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create config with storage
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	testIPs := []string{"***********", "********", "127.0.0.1", "::1"}

	for _, ipStr := range testIPs {
		t.Run(ipStr, func(t *testing.T) {
			ip := net.ParseIP(ipStr)
			if ip == nil {
				t.Fatalf("Failed to parse IP: %s", ipStr)
			}

			cert, err := au.Issue(ipStr)
			if err != nil {
				t.Fatalf("Failed to create certificate for IP %s: %v", ipStr, err)
			}

			if cert.Leaf == nil {
				t.Fatal("Certificate leaf should not be nil")
			}

			// Verify IP address is in certificate
			found := false
			for _, certIP := range cert.Leaf.IPAddresses {
				if certIP.Equal(ip) {
					found = true
					break
				}
			}

			if !found {
				t.Errorf("IP address %s not found in certificate. Got: %v", ipStr, cert.Leaf.IPAddresses)
			}
		})
	}
}

// TestCertificateChainValidation tests certificate chain validation
func TestCertificateChainValidation(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create config with storage
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	domain := "chain-test.example.com"
	cert, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to create certificate: %v", err)
	}

	// Verify certificate chain
	if len(cert.Certificate) < 2 {
		t.Fatal("Certificate chain should contain at least 2 certificates (leaf + CA)")
	}

	// Parse leaf certificate
	leafCert, err := x509.ParseCertificate(cert.Certificate[0])
	if err != nil {
		t.Fatalf("Failed to parse leaf certificate: %v", err)
	}

	// Parse CA certificate
	caCert, err := x509.ParseCertificate(cert.Certificate[1])
	if err != nil {
		t.Fatalf("Failed to parse CA certificate: %v", err)
	}

	// Verify leaf certificate is signed by CA
	if err := leafCert.CheckSignatureFrom(caCert); err != nil {
		t.Errorf("Leaf certificate signature verification failed: %v", err)
	}

	// Verify certificate chain using standard validation
	roots := x509.NewCertPool()
	roots.AddCert(caCert)

	opts := x509.VerifyOptions{
		DNSName: domain,
		Roots:   roots,
	}

	chains, err := leafCert.Verify(opts)
	if err != nil {
		t.Errorf("Certificate chain verification failed: %v", err)
	}

	if len(chains) == 0 {
		t.Error("No valid certificate chains found")
	}
}

// TestCertificateRenewal tests certificate caching behavior
func TestCertificateRenewal(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	domain := "renewal-test.example.com"

	// Generate first certificate
	cert1, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to create first certificate: %v", err)
	}

	// Issue certificate again (should be from cache)
	cert2, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to get cached certificate: %v", err)
	}

	// Certificates should be the same (from cache)
	if cert1 != cert2 {
		t.Error("Should return the same certificate from cache")
	}

	// Serial numbers should be the same
	if cert1.Leaf.SerialNumber.Cmp(cert2.Leaf.SerialNumber) != 0 {
		t.Error("Cached certificate should have the same serial number")
	}

	// Manually remove from cache to test storage retrieval
	expectedKey, _ := extractFromHost(domain, nil, nil)
	au.cache.Delete(expectedKey)

	// Issue certificate again (should be from storage)
	cert3, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to get certificate from storage: %v", err)
	}

	// Should have the same serial number as the original
	if cert1.Leaf.SerialNumber.Cmp(cert3.Leaf.SerialNumber) != 0 {
		t.Error("Certificate from storage should have the same serial number as original")
	}
}

// TestExtractFromHost tests the extractFromHost function
func TestExtractFromHost(t *testing.T) {
	tests := []struct {
		host     string
		expected string
	}{
		{"example.com", "example.com"},
		{"www.example.com", "example.com"},
		{"api.www.example.com", "example.com"},
		{"sub.domain.example.com", "example.com"},
		{"***********", "***********"},
		{"::1", "::1"},
		{"localhost", "localhost"},
		{"a.b", "a.b"},
	}

	for _, test := range tests {
		t.Run(test.host, func(t *testing.T) {
			result, _ := extractFromHost(test.host, nil, nil)
			if result != test.expected {
				t.Errorf("extractFromHost(%s) = %s, expected %s", test.host, result, test.expected)
			}
		})
	}
}

// TestValidateFunction tests the validate function
func TestValidateFunction(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Generate a valid certificate
	domain := "validate-test.example.com"
	cert, err := au.Issue(domain)
	if err != nil {
		t.Fatalf("Failed to create certificate: %v", err)
	}

	// Test valid certificate
	if err := validate(cert, domain); err != nil {
		t.Errorf("Valid certificate should pass validation: %v", err)
	}

	// Generate an ip certificate
	ipCert, err := au.Issue("***********")
	if err != nil {
		t.Fatalf("Failed to create ip certificate: %v", err)
	}

	// Test valid ip certificate
	if err := validate(ipCert, "***********"); err != nil {
		t.Errorf("Valid ip certificate should pass validation: %v", err)
	}

	// Test nil certificate
	if err := validate(nil, domain); err != errNilCert {
		t.Errorf("Expected errNilCert, got %v", err)
	}

	// Test certificate with nil leaf
	certWithoutLeaf := &tls.Certificate{
		Certificate: cert.Certificate,
		PrivateKey:  cert.PrivateKey,
		Leaf:        nil,
	}
	if err := validate(certWithoutLeaf, domain); err != errNilLeaf {
		t.Errorf("Expected errNilLeaf, got %v", err)
	}
}

// TestNewTLSConfig tests the NewTLSConfig method
func TestNewTLSConfig(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Get TLS config
	tlsConfig := au.NewTLSConfig()
	if tlsConfig == nil {
		t.Fatal("TLS config should not be nil")
	}

	if tlsConfig.GetCertificate == nil {
		t.Fatal("GetCertificate function should not be nil")
	}

	// Test GetCertificate function
	clientHello := &tls.ClientHelloInfo{
		ServerName: "tls-test.example.com",
	}

	cert, err := tlsConfig.GetCertificate(clientHello)
	if err != nil {
		t.Fatalf("GetCertificate failed: %v", err)
	}

	if cert == nil {
		t.Fatal("Certificate should not be nil")
	}

	if cert.Leaf == nil {
		t.Fatal("Certificate leaf should not be nil")
	}
}

// TestAuthorityClose tests the Close method
func TestAuthorityClose(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}

	// Generate a certificate to ensure background goroutines are running
	_, err = au.Issue("close-test.example.com")
	if err != nil {
		t.Fatalf("Failed to create certificate: %v", err)
	}

	// Close authority
	err = au.Close()
	if err != nil {
		t.Errorf("Close should not return error: %v", err)
	}

	// Calling Close again should not cause issues
	err = au.Close()
	if err != nil {
		t.Errorf("Second Close should not return error: %v", err)
	}

	// Trying to issue certificate after close should fail
	_, err = au.Issue("after-close.example.com")
	if err == nil {
		t.Error("Issue should fail after Close")
	}
}

// TestStorageEviction tests the certificate eviction functionality
func TestStorageEviction(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Generate some certificates
	domains := []string{"evict1.example.com", "evict2.example.com"}
	for _, domain := range domains {
		_, err := au.Issue(domain)
		if err != nil {
			t.Fatalf("Failed to create certificate for %s: %v", domain, err)
		}
	}

	// Test eviction manually
	err = au.storage.evictCerts()
	if err != nil {
		t.Errorf("Eviction should not fail: %v", err)
	}

	// Certificates should still be loadable (they're not expired)
	for _, domain := range domains {
		expectedKey, _ := extractFromHost(domain, nil, nil)
		_, err := au.storage.loadCert(expectedKey)
		if err != nil {
			t.Errorf("Certificate for %s should still be loadable after eviction: %v", domain, err)
		}
	}
}

// TestEmptyHostIssue tests Issue with empty host
func TestEmptyHostIssue(t *testing.T) {
	// Create temporary database
	tempFile, err := os.CreateTemp("", "test_cert_*.db")
	if err != nil {
		t.Fatalf("Failed to create temp file: %v", err)
	}
	tempFile.Close()
	defer os.Remove(tempFile.Name())

	// Create authority
	au, err := NewAuthority(tempFile.Name())
	if err != nil {
		t.Fatalf("Failed to create authority: %v", err)
	}
	defer au.Close()

	// Test empty host
	_, err = au.Issue("")
	if err == nil {
		t.Error("Issue with empty host should fail")
	}
}

// TestUniqueBy tests the uniqueBy function
func TestUniqueBy(t *testing.T) {
	// Test with strings
	strings := []string{"a", "b", "a", "c", "b", "d"}
	unique := uniqueBy(strings, func(s string) string { return s })
	expected := []string{"a", "b", "c", "d"}

	if len(unique) != len(expected) {
		t.Errorf("Expected %d unique strings, got %d", len(expected), len(unique))
	}

	for i, s := range expected {
		if i >= len(unique) || unique[i] != s {
			t.Errorf("Expected unique[%d] = %s, got %s", i, s, unique[i])
		}
	}

	// Test with empty slice
	empty := uniqueBy([]string{}, func(s string) string { return s })
	if len(empty) != 0 {
		t.Error("uniqueBy with empty slice should return empty slice")
	}
}

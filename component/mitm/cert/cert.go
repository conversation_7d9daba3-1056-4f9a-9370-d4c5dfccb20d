package cert

import (
	"crypto/rand"
	"crypto/rsa"
	"crypto/tls"
	"crypto/x509"
	"crypto/x509/pkix"
	"errors"
	"fmt"
	"github.com/metacubex/mihomo/common/xsync"
	"github.com/metacubex/mihomo/log"
	"net"
	"sync"
	"time"
	"unsafe"

	"github.com/metacubex/mihomo/common/singleflight"
)

var (
	errNilCert  = fmt.Errorf("nil certificate")
	errNilLeaf  = fmt.Errorf("nil leaf certificate")
	errExpired  = fmt.Errorf("expired certificate")
	errNotFound = fmt.Errorf("unexisted certificate")
	randReader  = rand.Reader
)

const (
	name         = "mihomo"
	organization = "MetaCubeX"
	caValidity   = time.Hour * 24 * 365 * 8 // 8 years
	certValidity = time.Hour * 24 * 365     // 1 year
)

type Authority struct {
	caCert  *x509.Certificate
	caPriv  *rsa.PrivateKey
	storage *storage
	cache   xsync.Map[string, *tls.Certificate]
	group   singleflight.Group[string, *tls.Certificate]
	closed  sync.RWMutex
	once    sync.Once
}

// NewAuthority creates a new certificate authority
func NewAuthority(dbPath string) (*Authority, error) {
	// Create storage
	stor, err := newStorage(dbPath)
	if err != nil {
		return nil, fmt.Errorf("failed to create storage: %w", err)
	}

	ca, caKey, err := stor.loadCA()
	if err != nil {
		if errors.Is(err, errTimeout) || errors.Is(err, errInvalidMapping) {
			return nil, err
		}
		if err = stor.clear(); err == nil {
			if ca, caKey, err = createCA(); err == nil {
				err = stor.saveCA(ca, caKey)
			}
		}
	} else {
		if stor.evictCerts() != nil {
			log.Errorln("failed to evict certs: %v", err)
		}
	}

	if err != nil {
		stor.close()
		return nil, fmt.Errorf("failed to init authority: %w", err)
	}
	ret := &Authority{
		caCert:  ca,
		caPriv:  caKey,
		storage: stor,
	}

	return ret, nil
}

func (a *Authority) createCert(host string, olds ...*tls.Certificate) (*tls.Certificate, error) {
	priv, pub, err := generateKeyPair()
	if err != nil {
		return nil, fmt.Errorf("failed to generate key pair: %w", err)
	}

	var dnsNames []string
	var ips []net.IP
	commonName, _ := extractFromHost(host, &dnsNames, &ips)

	for _, cert := range olds {
		if cert != nil && cert.Leaf != nil {
			dnsNames = append(dnsNames, cert.Leaf.DNSNames...)
			ips = append(ips, cert.Leaf.IPAddresses...)
		}
	}

	dnsNames = uniqueBy(dnsNames, func(s string) string { return s })
	ips = uniqueBy(ips, func(ip net.IP) string { return unsafe.String(&ip[0], len(ip)) })

	now := time.Now()
	tmpl := &x509.Certificate{
		Subject: pkix.Name{
			CommonName:   commonName,
			Organization: []string{a.caCert.Subject.Organization[0]},
		},
		SubjectKeyId:          generateKeyID(pub),
		KeyUsage:              x509.KeyUsageKeyEncipherment | x509.KeyUsageDigitalSignature,
		ExtKeyUsage:           []x509.ExtKeyUsage{x509.ExtKeyUsageServerAuth, x509.ExtKeyUsageClientAuth},
		BasicConstraintsValid: true,
		IsCA:                  false, // Explicitly mark as end-entity certificate
		NotBefore:             now.Add(-time.Hour),
		NotAfter:              now.Add(certValidity),
		DNSNames:              dnsNames,
		IPAddresses:           ips,
		SignatureAlgorithm:    x509.SHA256WithRSA, // Explicitly use SHA-256
	}

	raw, err := x509.CreateCertificate(randReader, tmpl, a.caCert, pub, a.caPriv)
	if err != nil {
		return nil, fmt.Errorf("failed to create certificate: %w", err)
	}

	x509c, err := x509.ParseCertificate(raw)
	if err != nil {
		return nil, fmt.Errorf("failed to parse certificate: %w", err)
	}

	cert := &tls.Certificate{
		Certificate: [][]byte{raw, a.caCert.Raw},
		PrivateKey:  priv,
		Leaf:        x509c,
	}

	return cert, nil
}

// Close closes the authority and its storage
func (a *Authority) Close() error {
	var err error = nil
	a.once.Do(func() {
		a.closed.Lock()
		err = a.storage.close()
	})
	return err
}

// Certificate returns the CA certificate
func (a *Authority) Certificate() *x509.Certificate {
	return a.caCert
}

// NewTLSConfig creates a TLS config with dynamic certificate creation
func (a *Authority) NewTLSConfig() *tls.Config {
	return &tls.Config{
		GetCertificate: func(clientHello *tls.ClientHelloInfo) (*tls.Certificate, error) {
			return a.Issue(clientHello.ServerName)
		},
	}
}

// Issue retrieves or creates a certificate for the given hostname
func (a *Authority) Issue(host string) (cert *tls.Certificate, err error) {
	if len(host) == 0 {
		return nil, fmt.Errorf("empty host")
	}
	if !a.closed.TryRLock() {
		return nil, fmt.Errorf("authority closed")
	}
	defer a.closed.RUnlock()

	commonName, suffix := extractFromHost(host, nil, nil)
	getFromCache := func() (*tls.Certificate, error) {
		if cert, found := a.cache.Load(commonName); found {
			err := validate(cert, host)
			return cert, err
		}
		return nil, errNotFound
	}
	// Check cache first
	if cert, err = getFromCache(); err == nil {
		return cert, nil
	}

	cert, err, _ = a.group.Do(suffix, func() (cert *tls.Certificate, err error) {
		// Double check
		if cert, err = getFromCache(); err == nil {
			return cert, nil
		}

		cert, _ = a.cache.Compute(commonName, func(old *tls.Certificate, loaded bool) (*tls.Certificate, xsync.ComputeOp) {
			// Third check
			if loaded {
				cert = old
				if err = validate(old, host); err == nil {
					return old, xsync.CancelOp
				}
			} else {
				if cert, err = a.storage.loadCert(commonName); err == nil {
					if err = validate(cert, host); err == nil {
						return cert, xsync.UpdateOp
					}
				}
			}
			if cert, err = a.createCert(host, cert); err == nil {
				err = a.storage.saveCert(cert)
			}
			return cert, xsync.UpdateOp
		})

		return
	})

	return cert, err
}

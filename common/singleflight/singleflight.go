// copy and modify from "golang.org/x/sync/singleflight"

// Copyright 2013 The Go Authors. All rights reserved.
// Use of this source code is governed by a BSD-style
// license that can be found in the LICENSE file.

// Package singleflight provides a duplicate function call suppression
// mechanism.
package singleflight

import (
	"bytes"
	"errors"
	"fmt"
	"runtime"
	"runtime/debug"
	"sync"

	"github.com/metacubex/mihomo/common/xsync"
)

// errGoexit indicates the runtime.Goexit was called in
// the user given function.
var errGoexit = errors.New("runtime.Goexit was called")

// A panicError is an arbitrary value recovered from a panic
// with the stack trace during the execution of given function.
type panicError struct {
	value interface{}
	stack []byte
}

// Error implements error interface.
func (p *panicError) Error() string {
	return fmt.Sprintf("%v\n\n%s", p.value, p.stack)
}

func (p *panicError) Unwrap() error {
	err, ok := p.value.(error)
	if !ok {
		return nil
	}

	return err
}

func newPanicError(v interface{}) error {
	stack := debug.Stack()

	// The first line of the stack trace is of the form "goroutine N [status]:"
	// but by the time the panic reaches Do the goroutine may no longer exist
	// and its status will have changed. Trim out the misleading line.
	if line := bytes.IndexByte(stack[:], '\n'); line >= 0 {
		stack = stack[line+1:]
	}
	return &panicError{value: v, stack: stack}
}

// call is an in-flight or completed singleflight.Do call
type call[T any] struct {
	wg sync.WaitGroup

	// These fields are written once before the WaitGroup is done
	// and are only read after the WaitGroup is done.
	val T
	err error

	// These fields are read and written with the singleflight
	// mutex held before the WaitGroup is done, and are read but
	// not written after the WaitGroup is done.
	dups  int
	chans []chan<- Result[T]
	fire  bool
}

// Group represents a class of work and forms a namespace in
// which units of work can be executed with duplicate suppression.
type Group[K comparable, T any] struct {
	m xsync.Map[K, *call[T]] // lazily initialized

	StoreResult bool
}

// Result holds the results of Do, so they can be passed
// on a channel.
type Result[T any] struct {
	Val    T
	Err    error
	Shared bool
}

// Do executes and returns the results of the given function, making
// sure that only one execution is in-flight for a given key at a
// time. If a duplicate comes in, the duplicate caller waits for the
// original to complete and receives the same results.
// The return value shared indicates whether v was given to multiple callers.
func (g *Group[K, T]) Do(key K, fn func() (T, error)) (v T, err error, shared bool) {
	wait := true
	c, _ := g.m.Compute(key, func(oldValue *call[T], loaded bool) (newValue *call[T], op xsync.ComputeOp) {
		if loaded {
			oldValue.dups++
			return oldValue, xsync.CancelOp
		}
		newValue = &call[T]{}
		newValue.wg.Add(1)
		wait = false
		return newValue, xsync.UpdateOp
	})

	if wait {
		c.wg.Wait()
	} else {
		g.doCall(c, key, fn)
	}

	if e, ok := c.err.(*panicError); ok {
		panic(e)
	} else if c.err == errGoexit {
		runtime.Goexit()
	}

	return c.val, c.err, c.dups > 0
}

// DoChan is like Do but returns a channel that will receive the
// results when they are ready.
//
// The returned channel will not be closed.
func (g *Group[K, T]) DoChan(key K, fn func() (T, error)) <-chan Result[T] {
	ch := make(chan Result[T], 1)

	g.m.Compute(key, func(oldValue *call[T], loaded bool) (newValue *call[T], op xsync.ComputeOp) {
		if loaded {
			oldValue.dups++
			oldValue.chans = append(oldValue.chans, ch)
			return oldValue, xsync.CancelOp
		}
		newValue = &call[T]{chans: []chan<- Result[T]{ch}}
		newValue.wg.Add(1)
		go g.doCall(newValue, key, fn)
		return newValue, xsync.UpdateOp
	})

	return ch
}

// Go is like DoChan but it ignores the results.
func (g *Group[K, T]) Go(key K, fn func() (T, error)) {
	g.m.Compute(key, func(oldValue *call[T], loaded bool) (newValue *call[T], op xsync.ComputeOp) {
		if loaded {
			oldValue.dups++
			return oldValue, xsync.CancelOp
		}
		newValue = &call[T]{fire: true}
		newValue.wg.Add(1)
		go g.doCall(newValue, key, fn)
		return newValue, xsync.UpdateOp
	})
}

// doCall handles the single call for a key.
func (g *Group[K, T]) doCall(c *call[T], key K, fn func() (T, error)) {
	normalReturn := false
	recovered := false

	// use double-defer to distinguish panic from runtime.Goexit,
	// more details see https://golang.org/cl/134395
	defer func() {
		// the given function invoked runtime.Goexit
		if !normalReturn && !recovered {
			c.err = errGoexit
		}

		c.wg.Done()
		g.m.Compute(key, func(oldValue *call[T], loaded bool) (*call[T], xsync.ComputeOp) {
			if loaded && oldValue == c && !g.StoreResult {
				return nil, xsync.DeleteOp
			}
			return oldValue, xsync.CancelOp
		})

		if e, ok := c.err.(*panicError); ok {
			// In order to prevent the waiting channels from being blocked forever,
			// needs to ensure that this panic cannot be recovered.
			if len(c.chans) > 0 || c.fire {
				go panic(e)
				select {} // Keep this goroutine around so that it will appear in the crash dump.
			} else {
				panic(e)
			}
		} else if c.err == errGoexit {
			// Already in the process of goexit, no need to call again
		} else {
			// Normal return
			for _, ch := range c.chans {
				ch <- Result[T]{c.val, c.err, c.dups > 0}
			}
		}
	}()

	func() {
		defer func() {
			if !normalReturn {
				// Ideally, we would wait to take a stack trace until we've determined
				// whether this is a panic or a runtime.Goexit.
				//
				// Unfortunately, the only way we can distinguish the two is to see
				// whether the recover stopped the goroutine from terminating, and by
				// the time we know that, the part of the stack trace relevant to the
				// panic has been discarded.
				if r := recover(); r != nil {
					c.err = newPanicError(r)
				}
			}
		}()

		c.val, c.err = fn()
		normalReturn = true
	}()

	if !normalReturn {
		recovered = true
	}
}

// Forget tells the singleflight to forget about a key.  Future calls
// to Do for this key will call the function rather than waiting for
// an earlier call to complete.
func (g *Group[K, T]) Forget(key K) {
	g.m.Delete(key)
}

func (g *Group[K, T]) Reset() {
	g.m.Clear()
}

# 核心组件

## 目录
- [概述](#概述)
- [常量定义模块](#常量定义模块)
  - [核心接口定义](#核心接口定义)
  - [元数据结构](#元数据结构)
  - [规则接口](#规则接口)
  - [适配器类型](#适配器类型)
- [配置管理模块](#配置管理模块)
  - [配置结构体](#配置结构体)
  - [解析流程](#解析流程)
  - [验证机制](#验证机制)
  - [热重载实现](#热重载实现)
- [Hub控制中心](#hub控制中心)
  - [执行器组件](#执行器组件)
  - [路由控制器](#路由控制器)
  - [API接口](#api接口)
- [适配器模块](#适配器模块)
  - [入站适配器](#入站适配器)
  - [出站适配器](#出站适配器)
  - [代理组实现](#代理组实现)
- [组件库](#组件库)
  - [DNS组件](#dns组件)
  - [地理数据组件](#地理数据组件)
  - [嗅探组件](#嗅探组件)
  - [认证组件](#认证组件)
- [监听器模块](#监听器模块)
  - [协议监听器](#协议监听器)
  - [系统监听器](#系统监听器)
  - [监听器管理](#监听器管理)
- [传输层模块](#传输层模块)
  - [连接抽象](#连接抽象)
  - [包处理](#包处理)
  - [缓冲管理](#缓冲管理)
- [隧道模块](#隧道模块)
  - [核心隧道](#核心隧道)
  - [连接处理](#连接处理)
  - [NAT表管理](#nat表管理)
  - [统计监控](#统计监控)

## 概述

Mihomo的核心组件采用模块化设计，每个模块负责特定的功能领域。组件间通过明确定义的接口进行交互，确保了系统的可扩展性和可维护性。

### 组件架构原则

1. **单一职责**: 每个组件专注于特定功能领域
2. **接口隔离**: 通过接口定义组件间的交互
3. **依赖倒置**: 高层模块不依赖低层模块的具体实现
4. **开闭原则**: 对扩展开放，对修改封闭

### 组件依赖关系

```mermaid
graph TD
    A[constant 常量定义] --> B[config 配置管理]
    A --> C[adapter 适配器]
    A --> D[rules 规则引擎]
    A --> E[dns DNS解析]

    B --> F[hub 控制中心]
    C --> F
    D --> F
    E --> F

    F --> G[tunnel 隧道核心]
    F --> H[listener 监听器]

    G --> I[statistic 统计监控]
    H --> I

    J[component 组件库] --> C
    J --> D
    J --> E
    J --> G
```

## 常量定义模块

### 位置
`constant/` 目录

### 作用
定义系统中使用的常量、接口和类型定义，为其他模块提供统一的数据结构。作为整个系统的基础层，提供类型安全的接口定义和枚举常量。

### 核心接口定义

#### ProxyAdapter接口 (`constant/adapters.go`)

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
type ProxyAdapter interface {
    Name() string
    Type() AdapterType
    Addr() string
    SupportUDP() bool

    // 核心连接方法
    DialContext(ctx context.Context, metadata *Metadata) (Conn, error)
    ListenPacketContext(ctx context.Context, metadata *Metadata) (PacketConn, error)

    // 协议特定方法
    StreamConnContext(ctx context.Context, c net.Conn, metadata *Metadata) (net.Conn, error)
    SupportUOT() bool
    SupportWithDialer() NetWork
    DialContextWithDialer(ctx context.Context, dialer Dialer, metadata *Metadata) (Conn, error)
    ListenPacketWithDialer(ctx context.Context, dialer Dialer, metadata *Metadata) (PacketConn, error)

    // 协议判断和代理链
    IsL3Protocol(metadata *Metadata) bool
    Unwrap(metadata *Metadata, touch bool) Proxy

    // 生命周期管理
    Close() error
}
````
</augment_code_snippet>

#### 连接接口定义

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
type Conn interface {
    N.ExtendedConn
    Connection
}

type PacketConn interface {
    N.EnhancePacketConn
    Connection
    ResolveUDP(ctx context.Context, metadata *Metadata) error
}

type Connection interface {
    Chains() Chain
    AppendToChains(adapter ProxyAdapter)
    RemoteDestination() string
}
````
</augment_code_snippet>

#### 适配器类型枚举

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
// AdapterType 代理适配器类型枚举
type AdapterType int

const (
    // 基础类型
    Direct AdapterType = iota  // 直连
    Reject                     // 拒绝连接
    RejectDrop                 // 静默丢弃
    Compatible                 // 兼容模式
    Pass                       // 透传
    Dns                        // DNS查询

    // 代理组类型
    Relay                      // 中继
    Selector                   // 手动选择
    Fallback                   // 故障转移
    URLTest                    // 延迟测试
    LoadBalance               // 负载均衡

    // 代理协议类型
    Shadowsocks               // Shadowsocks
    ShadowsocksR             // ShadowsocksR
    Snell                    // Snell
    Socks5                   // SOCKS5
    Http                     // HTTP代理
    Vmess                    // VMess
    Vless                    // VLESS
    Trojan                   // Trojan
    Hysteria                 // Hysteria
    Hysteria2                // Hysteria2
    WireGuard                // WireGuard
    Tuic                     // TUIC
    Ssh                      // SSH隧道
    Mieru                    // Mieru
    AnyTLS                   // AnyTLS
)
````
</augment_code_snippet>

### 元数据结构

#### Metadata结构定义 (`constant/metadata.go`)

<augment_code_snippet path="constant/metadata.go" mode="EXCERPT">
````go
// Metadata 连接元数据，包含连接的所有相关信息
type Metadata struct {
    NetWork      NetWork    `json:"network"`           // 网络类型(TCP/UDP)
    Type         Type       `json:"type"`              // 协议类型
    SrcIP        netip.Addr `json:"sourceIP"`          // 源IP地址
    DstIP        netip.Addr `json:"destinationIP"`     // 目标IP地址
    SrcGeoIP     []string   `json:"sourceGeoIP"`       // 源IP地理位置
    DstGeoIP     []string   `json:"destinationGeoIP"`  // 目标IP地理位置
    SrcIPASN     string     `json:"sourceIPASN"`       // 源IP ASN信息
    DstIPASN     string     `json:"destinationIPASN"`  // 目标IP ASN信息
    SrcPort      uint16     `json:"sourcePort,string"` // 源端口
    DstPort      uint16     `json:"destinationPort,string"` // 目标端口
    InIP         netip.Addr `json:"inboundIP"`         // 入站IP
    InPort       uint16     `json:"inboundPort,string"` // 入站端口
    InName       string     `json:"inboundName"`       // 入站名称
    InUser       string     `json:"inboundUser"`       // 入站用户
    Host         string     `json:"host"`              // 主机名
    DNSMode      DNSMode    `json:"dnsMode"`           // DNS模式
    Uid          uint32     `json:"uid"`               // 用户ID
    Process      string     `json:"process"`           // 进程名
    ProcessPath  string     `json:"processPath"`       // 进程路径
    SpecialProxy string     `json:"specialProxy"`      // 特殊代理
    SpecialRules string     `json:"specialRules"`      // 特殊规则
    RemoteDst    string     `json:"remoteDestination"` // 远程目标
    DSCP         uint8      `json:"dscp"`              // DSCP标记

    // 内部使用字段
    RawSrcAddr net.Addr `json:"-"`                     // 原始源地址
    RawDstAddr net.Addr `json:"-"`                     // 原始目标地址
    SniffHost  string   `json:"sniffHost"`             // 嗅探主机名
}
````
</augment_code_snippet>

#### 网络类型和协议类型

<augment_code_snippet path="constant/metadata.go" mode="EXCERPT">
````go
// NetWork 网络类型
const (
    TCP NetWork = iota
    UDP
    ALLNet
    InvalidNet = 0xff
)

// Type 协议类型
const (
    HTTP Type = iota      // HTTP代理
    HTTPS                 // HTTPS代理
    SOCKS4                // SOCKS4协议
    SOCKS5                // SOCKS5协议
    SHADOWSOCKS           // Shadowsocks协议
    VMESS                 // VMess协议
    VLESS                 // VLESS协议
    REDIR                 // 重定向
    TPROXY                // 透明代理
    TROJAN                // Trojan协议
    TUNNEL                // 隧道
    TUN                   // TUN接口
    TUIC                  // TUIC协议
    HYSTERIA2             // Hysteria2协议
    ANYTLS                // AnyTLS协议
    INNER                 // 内部协议
)
````
</augment_code_snippet>

### 规则接口

#### Rule接口定义 (`constant/rule.go`)

<augment_code_snippet path="constant/rule.go" mode="EXCERPT">
````go
// Rule 规则接口，定义规则匹配的标准方法
type Rule interface {
    RuleType() RuleType                                              // 规则类型
    Match(metadata *Metadata, helper RuleMatchHelper) (bool, string) // 规则匹配
    Adapter() string                                                 // 目标适配器
    Payload() string                                                 // 规则载荷
    ProviderNames() []string                                         // 提供者名称
}

// RuleMatchHelper 规则匹配辅助器
type RuleMatchHelper struct {
    ResolveIP   func()  // IP解析函数
    FindProcess func()  // 进程查找函数
}

// RuleGroup 规则组接口
type RuleGroup interface {
    Rule
    GetRecodeSize() int  // 获取记录大小
}
````
</augment_code_snippet>

#### 规则类型枚举

```go
// RuleType 规则类型枚举
type RuleType int

const (
    Domain RuleType = iota        // 域名规则
    DomainSuffix                  // 域名后缀规则
    DomainKeyword                 // 域名关键字规则
    GEOSITE                       // GeoSite规则
    GEOIP                         // GeoIP规则
    IPCIDR                        // IP CIDR规则
    SrcIPCIDR                     // 源IP CIDR规则
    SrcPort                       // 源端口规则
    DstPort                       // 目标端口规则
    ProcessName                   // 进程名规则
    ProcessPath                   // 进程路径规则
    ProcessNameRegex              // 进程名正则规则
    ProcessPathRegex              // 进程路径正则规则
    MATCH                         // 匹配所有
    RuleSet                       // 规则集
    Network                       // 网络类型规则
    DSCP                          // DSCP规则
    Uid                           // 用户ID规则
    SubRules                      // 子规则
    AND                           // 逻辑与
    OR                            // 逻辑或
    NOT                           // 逻辑非
)
```

### 适配器类型

#### 延迟历史和状态

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
// DelayHistory 延迟历史记录
type DelayHistory struct {
    Time  time.Time `json:"time"`   // 测试时间
    Delay uint16    `json:"delay"`  // 延迟值(毫秒)
}

// ProxyState 代理状态
type ProxyState struct {
    Alive   bool           `json:"alive"`   // 是否可用
    History []DelayHistory `json:"history"` // 延迟历史
}
````
</augment_code_snippet>

#### 代理接口扩展

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
// Proxy 代理接口，扩展了ProxyAdapter
type Proxy interface {
    ProxyAdapter
    Adapter() ProxyAdapter                                                    // 获取适配器
    AliveForTestUrl(url string) bool                                         // 检查URL可用性
    DelayHistory() []DelayHistory                                            // 延迟历史
    ExtraDelayHistories() map[string]ProxyState                             // 额外延迟历史
    LastDelayForTestUrl(url string) uint16                                   // 最后延迟
    URLTest(ctx context.Context, url string, expectedStatus utils.IntRanges[uint16]) (uint16, error) // URL测试

    // 已弃用方法
    Dial(metadata *Metadata) (Conn, error)                                  // 拨号连接
    DialUDP(metadata *Metadata) (PacketConn, error)                         // UDP拨号
}

// Group 代理组接口
type Group interface {
    URLTest(ctx context.Context, url string, expectedStatus utils.IntRanges[uint16]) (mp map[string]uint16, err error) // 组URL测试
    Touch()                                                                  // 触摸更新
}
````
</augment_code_snippet>

### 上下文接口

#### 连接上下文 (`constant/context.go`)

<augment_code_snippet path="constant/context.go" mode="EXCERPT">
````go
// PlainContext 基础上下文接口
type PlainContext interface {
    ID() uuid.UUID  // 唯一标识符
}

// ConnContext TCP连接上下文接口
type ConnContext interface {
    PlainContext
    Metadata() *Metadata           // 连接元数据
    Conn() *N.BufferedConn        // 缓冲连接
}

// PacketConnContext UDP包连接上下文接口
type PacketConnContext interface {
    PlainContext
    Metadata() *Metadata          // 包元数据
    PacketConn() net.PacketConn   // 包连接
}
````
</augment_code_snippet>

### 依赖关系
- **被依赖**: 所有其他模块都引用constant包
- **依赖**: 仅依赖标准库和第三方基础库
- **作用**: 作为系统的类型定义基础，确保类型安全和接口一致性

## 配置管理模块

### 位置
`config/` 目录

### 作用
负责配置文件的解析、验证和管理，将YAML配置转换为内部数据结构。作为系统配置的中央管理器，提供类型安全的配置访问和动态更新能力。

### 配置结构体

#### 主配置结构 (`config/config.go`)

<augment_code_snippet path="config/config.go" mode="EXCERPT">
````go
// Config 主配置结构，包含所有配置项
type Config struct {
    General       *General                                    // 通用配置
    Controller    *Controller                                 // 控制器配置
    Experimental  *Experimental                               // 实验性功能
    IPTables      *IPTables                                   // iptables配置
    NTP           *NTP                                        // NTP时间同步
    DNS           *DNS                                        // DNS配置
    Hosts         *trie.DomainTrie[resolver.HostValue]       // 主机映射
    Profile       *Profile                                    // 配置文件信息
    Rules         []C.Rule                                    // 规则列表
    SubRules      map[string][]C.Rule                        // 子规则
    Users         []auth.AuthUser                            // 认证用户
    Proxies       map[string]C.Proxy                         // 代理节点
    Listeners     map[string]C.InboundListener               // 入站监听器
    Providers     map[string]providerTypes.ProxyProvider     // 代理提供者
    RuleProviders map[string]providerTypes.RuleProvider      // 规则提供者
    Tunnels       []LC.Tunnel                                // 隧道配置
    Sniffer       *sniffer.Config                            // 流量嗅探
    TLS           *TLS                                        // TLS配置
}
````
</augment_code_snippet>

#### 通用配置结构 (`config/config.go`)

<augment_code_snippet path="config/config.go" mode="EXCERPT">
````go
// General 通用配置
type General struct {
    Inbound                                                   // 入站配置
    Mode                    T.TunnelMode      `json:"mode"`   // 运行模式
    UnifiedDelay            bool              `json:"unified-delay"` // 统一延迟
    LogLevel                log.LogLevel      `json:"log-level"`     // 日志级别
    IPv6                    bool              `json:"ipv6"`          // IPv6支持
    Interface               string            `json:"interface-name"` // 网络接口
    RoutingMark             int               `json:"routing-mark"`   // 路由标记
    GeoXUrl                 GeoXUrl           `json:"geox-url"`      // 地理数据URL
    GeoAutoUpdate           bool              `json:"geo-auto-update"` // 自动更新地理数据
    GeoUpdateInterval       int               `json:"geo-update-interval"` // 更新间隔
    GeodataMode             bool              `json:"geodata-mode"`   // 地理数据模式
    GeodataLoader           string            `json:"geodata-loader"` // 地理数据加载器
    TCPConcurrent           bool              `json:"tcp-concurrent"` // TCP并发
    FindProcessMode         P.FindProcessMode `json:"find-process-mode"` // 进程查找模式
    GlobalClientFingerprint string            `json:"global-client-fingerprint"` // 全局客户端指纹
    GlobalUA                string            `json:"global-ua"`      // 全局User-Agent
    ETag                    string            `json:"etag"`           // ETag
}
````
</augment_code_snippet>

#### 入站配置结构

```go
// Inbound 入站配置
type Inbound struct {
    Port              int                 `json:"port"`               // HTTP代理端口
    SocksPort         int                 `json:"socks-port"`         // SOCKS代理端口
    RedirPort         int                 `json:"redir-port"`         // 重定向端口
    TProxyPort        int                 `json:"tproxy-port"`        // 透明代理端口
    MixedPort         int                 `json:"mixed-port"`         // 混合端口
    ShadowSocksConfig string              `json:"ss-config"`          // Shadowsocks配置
    VmessConfig       string              `json:"vmess-config"`       // VMess配置
    AllowLan          bool                `json:"allow-lan"`          // 允许局域网
    SkipAuthPrefixes  []netip.Prefix      `json:"skip-auth-prefixes"` // 跳过认证前缀
    LanAllowedIPs     []netip.Prefix      `json:"lan-allowed-ips"`    // 允许的局域网IP
    LanDisAllowedIPs  []netip.Prefix      `json:"lan-disallowed-ips"` // 禁止的局域网IP
    BindAddress       string              `json:"bind-address"`       // 绑定地址
    InboundTfo        bool                `json:"inbound-tfo"`        // 入站TCP Fast Open
    InboundMPTCP      bool                `json:"inbound-mptcp"`      // 入站MPTCP
}
```

#### DNS配置结构

```go
// DNS DNS配置
type DNS struct {
    Enable            bool                          `json:"enable"`             // 启用DNS
    IPv6              bool                          `json:"ipv6"`               // IPv6支持
    IPv6Timeout       uint                          `json:"ipv6-timeout"`       // IPv6超时
    UseHosts          bool                          `json:"use-hosts"`          // 使用hosts
    UseSystemHosts    bool                          `json:"use-system-hosts"`   // 使用系统hosts
    RespectRules      bool                          `json:"respect-rules"`      // 遵循规则
    Listen            string                        `json:"listen"`             // 监听地址
    PreferH3          bool                          `json:"prefer-h3"`          // 优先HTTP/3
    FakeIPRange       *fakeip.Pool                  `json:"fake-ip-range"`      // Fake IP范围
    FakeIPFilter      []string                      `json:"fake-ip-filter"`     // Fake IP过滤器
    FakeIPFilterMode  fakeip.FakeIPFilterMode       `json:"fake-ip-filter-mode"` // 过滤模式
    DefaultNameserver []dns.NameServer              `json:"default-nameserver"` // 默认DNS服务器
    NameServer        []dns.NameServer              `json:"nameserver"`         // DNS服务器
    Fallback          []dns.NameServer              `json:"fallback"`           // 备用DNS
    FallbackFilter    dns.FallbackFilter            `json:"fallback-filter"`    // 备用过滤器
    NameServerPolicy  map[string][]dns.NameServer   `json:"nameserver-policy"`  // DNS策略
    ProxyServerNameserver []dns.NameServer          `json:"proxy-server-nameserver"` // 代理服务器DNS
}
```

### 解析流程

#### 配置解析管道

```mermaid
flowchart TD
    A[YAML文件] --> B[YAML解析器]
    B --> C[RawConfig结构]
    C --> D[配置验证器]
    D --> E{验证通过?}
    E -->|否| F[错误处理]
    E -->|是| G[类型转换器]
    G --> H[默认值填充]
    H --> I[依赖关系检查]
    I --> J[Config结构]
    J --> K[组件初始化]

    F --> L[错误日志]
    L --> M[解析失败]
```

#### 解析函数实现

```go
// ParseWithPath 解析配置文件
func ParseWithPath(path string, configBytes []byte) (*Config, error) {
    config := &Config{}
    rawCfg, err := UnmarshalRawConfig(configBytes)
    if err != nil {
        return nil, err
    }

    config.General, err = parseGeneral(rawCfg)
    if err != nil {
        return nil, err
    }

    config.DNS, err = parseDNS(rawCfg.DNS, config.General.IPv6)
    if err != nil {
        return nil, err
    }

    config.Rules, err = parseRules(rawCfg.Rule, rawCfg.RuleProvider)
    if err != nil {
        return nil, err
    }

    config.Proxies, err = parseProxies(rawCfg.Proxy)
    if err != nil {
        return nil, err
    }

    return config, nil
}
```

### 验证机制

#### 配置验证器

```go
type ConfigValidator struct {
    errors []error
}

func (cv *ConfigValidator) ValidateGeneral(general *General) {
    if general.Port < 0 || general.Port > 65535 {
        cv.errors = append(cv.errors, fmt.Errorf("invalid port: %d", general.Port))
    }

    if general.SocksPort < 0 || general.SocksPort > 65535 {
        cv.errors = append(cv.errors, fmt.Errorf("invalid socks port: %d", general.SocksPort))
    }

    if general.LogLevel < log.DEBUG || general.LogLevel > log.SILENT {
        cv.errors = append(cv.errors, fmt.Errorf("invalid log level: %d", general.LogLevel))
    }
}

func (cv *ConfigValidator) ValidateDNS(dns *DNS) {
    if dns.Enable && dns.Listen == "" {
        cv.errors = append(cv.errors, errors.New("DNS listen address cannot be empty when DNS is enabled"))
    }

    if len(dns.NameServer) == 0 {
        cv.errors = append(cv.errors, errors.New("at least one nameserver must be configured"))
    }
}

func (cv *ConfigValidator) HasErrors() bool {
    return len(cv.errors) > 0
}

func (cv *ConfigValidator) Errors() []error {
    return cv.errors
}
```

#### 依赖关系验证

```go
func validateDependencies(config *Config) error {
    // 验证代理引用
    for _, rule := range config.Rules {
        adapter := rule.Adapter()
        if _, exists := config.Proxies[adapter]; !exists {
            return fmt.Errorf("rule references non-existent proxy: %s", adapter)
        }
    }

    // 验证代理组引用
    for name, proxy := range config.Proxies {
        if group, ok := proxy.(*outboundgroup.GroupBase); ok {
            for _, proxyName := range group.GetProxies(false) {
                if _, exists := config.Proxies[proxyName.Name()]; !exists {
                    return fmt.Errorf("proxy group %s references non-existent proxy: %s", name, proxyName.Name())
                }
            }
        }
    }

    return nil
}
```

### 热重载实现

#### 配置监控

```go
type ConfigWatcher struct {
    configPath string
    callback   func(*Config) error
    watcher    *fsnotify.Watcher
    stopCh     chan struct{}
}

func NewConfigWatcher(configPath string, callback func(*Config) error) (*ConfigWatcher, error) {
    watcher, err := fsnotify.NewWatcher()
    if err != nil {
        return nil, err
    }

    cw := &ConfigWatcher{
        configPath: configPath,
        callback:   callback,
        watcher:    watcher,
        stopCh:     make(chan struct{}),
    }

    err = watcher.Add(configPath)
    if err != nil {
        return nil, err
    }

    go cw.watch()
    return cw, nil
}

func (cw *ConfigWatcher) watch() {
    for {
        select {
        case event := <-cw.watcher.Events:
            if event.Op&fsnotify.Write == fsnotify.Write {
                cw.handleConfigChange()
            }
        case err := <-cw.watcher.Errors:
            log.Errorln("Config watcher error: %v", err)
        case <-cw.stopCh:
            return
        }
    }
}

func (cw *ConfigWatcher) handleConfigChange() {
    configBytes, err := os.ReadFile(cw.configPath)
    if err != nil {
        log.Errorln("Failed to read config file: %v", err)
        return
    }

    config, err := ParseWithPath(cw.configPath, configBytes)
    if err != nil {
        log.Errorln("Failed to parse config: %v", err)
        return
    }

    if err := cw.callback(config); err != nil {
        log.Errorln("Failed to apply config: %v", err)
    }
}
```

#### 增量更新策略

```go
type ConfigDiff struct {
    ProxiesAdded   map[string]C.Proxy
    ProxiesRemoved []string
    ProxiesChanged map[string]C.Proxy
    RulesChanged   bool
    DNSChanged     bool
    ListenersChanged bool
}

func ComputeConfigDiff(oldConfig, newConfig *Config) *ConfigDiff {
    diff := &ConfigDiff{
        ProxiesAdded:   make(map[string]C.Proxy),
        ProxiesRemoved: make([]string, 0),
        ProxiesChanged: make(map[string]C.Proxy),
    }

    // 检查代理变更
    for name, newProxy := range newConfig.Proxies {
        if oldProxy, exists := oldConfig.Proxies[name]; exists {
            if !proxyEquals(oldProxy, newProxy) {
                diff.ProxiesChanged[name] = newProxy
            }
        } else {
            diff.ProxiesAdded[name] = newProxy
        }
    }

    for name := range oldConfig.Proxies {
        if _, exists := newConfig.Proxies[name]; !exists {
            diff.ProxiesRemoved = append(diff.ProxiesRemoved, name)
        }
    }

    // 检查其他组件变更
    diff.RulesChanged = !rulesEquals(oldConfig.Rules, newConfig.Rules)
    diff.DNSChanged = !dnsEquals(oldConfig.DNS, newConfig.DNS)
    diff.ListenersChanged = !listenersEquals(oldConfig.Listeners, newConfig.Listeners)

    return diff
}
```

### 功能特性

1. **类型安全**: 强类型配置结构，编译时检查
2. **验证完整**: 多层次配置验证，确保配置正确性
3. **热重载**: 支持配置文件监控和动态更新
4. **默认值**: 智能默认值填充，简化配置
5. **错误处理**: 详细的错误信息和恢复机制
6. **增量更新**: 支持配置的增量更新，减少重启影响

### 依赖关系
- **依赖**: `constant`、`adapter`、`rules`、`dns`、`component`等
- **被依赖**: `hub/executor`、`hub/route`
- **作用**: 作为配置管理的中央枢纽，连接配置文件和运行时组件

## Hub控制中心

### 位置
`hub/` 目录

### 作用
系统的控制中心，负责配置解析、组件初始化和API服务。

### 关键组件

#### `hub/executor/executor.go`
执行器负责配置的应用和系统组件的初始化：

<augment_code_snippet path="hub/executor/executor.go" mode="EXCERPT">
````go
func Parse(configBytes []byte, options ...Option) error {
    return ParseWithPath("", configBytes, options...)
}

func ParseWithBytes(configBytes []byte) (*config.Config, error) {
    return ParseWithPath("", configBytes)
}
````
</augment_code_snippet>

#### `hub/route/`
RESTful API路由定义，提供：
- 配置管理API
- 代理状态查询
- 连接统计
- 规则管理

### 功能特性
- 配置热重载
- RESTful API服务
- WebUI支持
- 实时监控

## 适配器模块

### 位置
`adapter/` 目录

### 作用
为不同的代理协议提供统一的接口实现，包括入站和出站适配器。

### 子模块结构

#### `adapter/inbound/`
入站适配器，处理各种协议的入站连接。

#### `adapter/outbound/`
出站适配器，实现各种代理协议：

<augment_code_snippet path="adapter/outbound/base.go" mode="EXCERPT">
````go
type ProxyAdapter interface {
    C.ProxyAdapter
    DialOptions() []dialer.Option
    ResolveUDP(ctx context.Context, metadata *C.Metadata) error
}

type Base struct {
    name   string
    addr   string
    iface  string
    tp     C.AdapterType
    udp    bool
    xudp   bool
    tfo    bool
    mpTcp  bool
    rmark  int
    id     string
    prefer C.DNSPrefer
}
````
</augment_code_snippet>

#### `adapter/outboundgroup/`
代理组实现，支持多种负载均衡策略：
- Selector: 手动选择
- URLTest: 延迟测试自动选择
- Fallback: 故障转移
- LoadBalance: 负载均衡

#### `adapter/provider/`
代理提供者，支持从外部源获取代理列表。

### 设计特点
- 统一的接口设计
- 插件式架构
- 支持协议扩展

## 组件库

### 位置
`component/` 目录

### 作用
提供系统各个功能模块的具体实现。

### 主要组件

#### `component/auth/`
认证组件，支持HTTP和SOCKS代理的用户认证。

#### `component/dialer/`
拨号器组件，提供网络连接的建立功能。

#### `component/dns/`
DNS相关组件，已移至独立的`dns/`包。

#### `component/fakeip/`
Fake IP功能实现，用于DNS劫持和流量分析。

#### `component/geodata/`
地理位置数据处理，支持GeoIP和GeoSite数据库。

#### `component/resolver/`
域名解析器，提供增强的DNS解析功能。

#### `component/sniffer/`
流量嗅探器，用于协议检测和域名提取。

#### `component/tls/`
TLS相关功能，包括证书管理和TLS配置。

#### `component/trie/`
字典树实现，用于高效的域名匹配。

### 设计原则
- 单一职责
- 接口隔离
- 依赖注入

## 监听器模块

### 位置
`listener/` 目录

### 作用
实现各种协议的入站监听器，接收和处理客户端连接。

### 监听器类型

#### `listener/http/`
HTTP代理监听器

#### `listener/socks/`
SOCKS4/SOCKS5代理监听器

#### `listener/mixed/`
混合监听器，同时支持HTTP和SOCKS协议：

<augment_code_snippet path="listener/mixed/mixed.go" mode="EXCERPT">
````go
type Listener struct {
    listener net.Listener
    addr     string
    closed   bool
}
````
</augment_code_snippet>

#### `listener/tproxy/`
透明代理监听器，支持TPROXY模式

#### `listener/sing_tun/`
TUN接口监听器，基于sing-tun实现

#### `listener/sing_vmess/`、`listener/sing_vless/`
协议特定监听器

### 特性
- 协议自动检测
- TLS支持
- 认证集成
- 统计功能

## 传输层模块

### 位置
`transport/` 目录

### 作用
实现各种代理协议的传输层逻辑。

### 协议实现

#### `transport/vmess/`
VMess协议实现：

<augment_code_snippet path="transport/vmess/vmess.go" mode="EXCERPT">
````go
// Client is vmess connection generator
type Client struct {
    user     []*ID
    uuid     *uuid.UUID
    security Security
    isAead   bool
}
````
</augment_code_snippet>

#### `transport/vless/`
VLESS协议实现

#### `transport/trojan/`
Trojan协议实现

#### `transport/shadowsocks/`
Shadowsocks协议实现

#### `transport/hysteria/`
Hysteria协议实现

#### `transport/tuic/`
TUIC协议实现

### 设计特点
- 协议无关的接口设计
- 支持多种加密方式
- 优化的性能实现

## 隧道模块

### 位置
`tunnel/` 目录

### 作用
系统的核心数据处理模块，负责连接的路由和转发。

### 关键功能

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func (t tunnel) HandleTCPConn(conn net.Conn, metadata *C.Metadata) {
    connCtx := icontext.NewConnContext(conn, metadata)
    handleTCPConn(connCtx)
}

func (t tunnel) HandleUDPPacket(packet C.UDPPacket, metadata *C.Metadata) {
    udpInit.Do(initUDP)
    
    packetAdapter := C.NewPacketAdapter(packet, metadata)
    key := packetAdapter.Key()
    
    hash := utils.MapHash(key)
    queueNo := uint(hash) % uint(len(udpQueues))
    
    select {
    case udpQueues[queueNo] <- packetAdapter:
    default:
        packet.Drop()
    }
}
````
</augment_code_snippet>

### 功能特性
- TCP/UDP连接处理
- NAT表管理
- 统计信息收集
- 连接状态管理

### 依赖关系
- 依赖：所有其他核心模块
- 被依赖：`listener`模块

这些核心组件共同构成了Mihomo的完整功能体系，通过清晰的模块划分和接口设计，确保了系统的高性能和可扩展性。

# 测试和开发

## 目录
- [概述](#概述)
- [开发环境搭建](#开发环境搭建)
- [代码结构和规范](#代码结构和规范)
- [单元测试](#单元测试)
- [集成测试](#集成测试)
- [性能测试](#性能测试)
- [调试技巧](#调试技巧)
- [贡献指南](#贡献指南)
- [发布流程](#发布流程)

## 概述

Mihomo项目采用现代化的Go开发实践，包括完整的测试体系、代码质量检查和持续集成。本文档为开发者提供了参与项目开发所需的全部信息。

## 开发环境搭建

### 1. 基础环境要求

```bash
# Go版本要求
go version  # 需要Go 1.20+

# 安装开发工具
go install golang.org/x/tools/cmd/goimports@latest
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
go install github.com/securecodewarrior/sast-scan@latest
```

### 2. 项目克隆和设置

```bash
# 克隆项目
git clone https://github.com/MetaCubeX/mihomo.git
cd mihomo

# 安装依赖
go mod download

# 验证构建
make build

# 运行测试
make test
```

### 3. IDE配置

#### VS Code配置

```json
// .vscode/settings.json
{
    "go.useLanguageServer": true,
    "go.lintTool": "golangci-lint",
    "go.lintFlags": [
        "--fast"
    ],
    "go.testFlags": ["-v"],
    "go.testTimeout": "30s",
    "go.coverOnSave": true,
    "go.coverageDecorator": {
        "type": "gutter"
    }
}
```

#### GoLand配置

```xml
<!-- .idea/runConfigurations/mihomo.xml -->
<component name="ProjectRunConfigurationManager">
  <configuration default="false" name="mihomo" type="GoApplicationRunConfiguration">
    <module name="mihomo" />
    <working_directory value="$PROJECT_DIR$" />
    <parameters value="-d test/config" />
    <kind value="PACKAGE" />
    <package value="github.com/metacubex/mihomo" />
    <directory value="$PROJECT_DIR$" />
  </configuration>
</component>
```

## 代码结构和规范

### 1. 项目结构

```
mihomo/
├── adapter/           # 适配器实现
│   ├── inbound/      # 入站适配器
│   ├── outbound/     # 出站适配器
│   └── outboundgroup/ # 代理组
├── common/           # 通用工具库
├── component/        # 功能组件
├── config/           # 配置管理
├── constant/         # 常量定义
├── dns/             # DNS解析器
├── hub/             # 控制中心
├── listener/        # 监听器
├── rules/           # 规则引擎
├── transport/       # 传输层
├── tunnel/          # 隧道核心
└── test/            # 测试文件
```

### 2. 代码规范

#### 命名规范

```go
// 包名：小写，简洁
package outbound

// 接口名：名词，首字母大写
type ProxyAdapter interface {
    Name() string
    Type() C.AdapterType
}

// 结构体：名词，首字母大写
type VmessOption struct {
    Name     string `proxy:"name"`
    Server   string `proxy:"server"`
    Port     int    `proxy:"port"`
}

// 方法名：动词，首字母大写
func (v *Vmess) DialContext(ctx context.Context, metadata *C.Metadata) (C.Conn, error) {
    // 实现
}

// 常量：大写，下划线分隔
const (
    DefaultTCPTimeout = 5 * time.Second
    MaxRetryTimes     = 3
)
```

#### 错误处理

```go
// 使用标准错误处理模式
func parseConfig(data []byte) (*Config, error) {
    var config Config
    if err := yaml.Unmarshal(data, &config); err != nil {
        return nil, fmt.Errorf("parse config failed: %w", err)
    }
    
    if err := config.validate(); err != nil {
        return nil, fmt.Errorf("validate config failed: %w", err)
    }
    
    return &config, nil
}

// 自定义错误类型
type ConfigError struct {
    Field string
    Value interface{}
    Err   error
}

func (e *ConfigError) Error() string {
    return fmt.Sprintf("config field %s with value %v: %v", e.Field, e.Value, e.Err)
}
```

#### 并发安全

```go
// 使用sync包保证并发安全
type SafeMap struct {
    mu   sync.RWMutex
    data map[string]interface{}
}

func (m *SafeMap) Get(key string) (interface{}, bool) {
    m.mu.RLock()
    defer m.mu.RUnlock()
    val, ok := m.data[key]
    return val, ok
}

func (m *SafeMap) Set(key string, value interface{}) {
    m.mu.Lock()
    defer m.mu.Unlock()
    m.data[key] = value
}
```

## 单元测试

### 1. 测试文件结构

```go
// adapter/outbound/vmess_test.go
package outbound

import (
    "context"
    "testing"
    "time"
    
    C "github.com/metacubex/mihomo/constant"
    "github.com/stretchr/testify/assert"
    "github.com/stretchr/testify/require"
)

func TestVmess_DialContext(t *testing.T) {
    tests := []struct {
        name     string
        option   VmessOption
        metadata *C.Metadata
        wantErr  bool
    }{
        {
            name: "valid connection",
            option: VmessOption{
                Name:   "test",
                Server: "example.com",
                Port:   443,
                UUID:   "test-uuid",
            },
            metadata: &C.Metadata{
                Host:    "target.com",
                DstPort: "80",
            },
            wantErr: false,
        },
        {
            name: "invalid server",
            option: VmessOption{
                Name:   "test",
                Server: "",
                Port:   443,
            },
            wantErr: true,
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            v, err := NewVmess(tt.option)
            require.NoError(t, err)
            
            ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
            defer cancel()
            
            conn, err := v.DialContext(ctx, tt.metadata)
            if tt.wantErr {
                assert.Error(t, err)
                assert.Nil(t, conn)
            } else {
                assert.NoError(t, err)
                assert.NotNil(t, conn)
                if conn != nil {
                    conn.Close()
                }
            }
        })
    }
}
```

### 2. Mock和测试工具

```go
// test/mock/proxy.go
package mock

import (
    "context"
    "net"
    
    C "github.com/metacubex/mihomo/constant"
)

type MockProxy struct {
    name     string
    dialFunc func(ctx context.Context, metadata *C.Metadata) (C.Conn, error)
}

func (m *MockProxy) Name() string {
    return m.name
}

func (m *MockProxy) Type() C.AdapterType {
    return C.Direct
}

func (m *MockProxy) DialContext(ctx context.Context, metadata *C.Metadata) (C.Conn, error) {
    if m.dialFunc != nil {
        return m.dialFunc(ctx, metadata)
    }
    return &MockConn{}, nil
}

type MockConn struct {
    net.Conn
}

func (m *MockConn) Read(b []byte) (n int, err error) {
    return len(b), nil
}

func (m *MockConn) Write(b []byte) (n int, err error) {
    return len(b), nil
}

func (m *MockConn) Close() error {
    return nil
}
```

### 3. 基准测试

```go
// dns/resolver_test.go
func BenchmarkResolver_Exchange(b *testing.B) {
    resolver := &Resolver{
        main: []dnsClient{
            &mockDNSClient{},
        },
        cache: newCache(),
    }
    
    msg := &dns.Msg{}
    msg.SetQuestion(dns.Fqdn("example.com"), dns.TypeA)
    
    b.ResetTimer()
    b.RunParallel(func(pb *testing.PB) {
        for pb.Next() {
            _, _ = resolver.Exchange(context.Background(), msg)
        }
    })
}

func BenchmarkCache_Get(b *testing.B) {
    cache := newCache()
    key := "example.com."
    msg := &dns.Msg{}
    cache.Set(key, msg, time.Hour)
    
    b.ResetTimer()
    for i := 0; i < b.N; i++ {
        _, _, _ = cache.GetWithExpire(key)
    }
}
```

## 集成测试

### 1. 测试环境搭建

<augment_code_snippet path="test/README.md" mode="EXCERPT">
````markdown
# 测试环境说明

## 依赖服务
- Docker和Docker Compose
- 测试代理服务器
- Mock DNS服务器

## 运行测试
```bash
cd test
make test-all
```
````
</augment_code_snippet>

### 2. 端到端测试

```go
// test/e2e_test.go
package test

import (
    "context"
    "net/http"
    "testing"
    "time"
    
    "github.com/metacubex/mihomo/hub/executor"
    "github.com/stretchr/testify/assert"
)

func TestE2E_HTTPProxy(t *testing.T) {
    // 启动mihomo实例
    configPath := "config/test-http.yaml"
    cfg, err := executor.ParseWithPath(configPath, nil)
    assert.NoError(t, err)
    
    defer executor.Shutdown()
    
    // 等待服务启动
    time.Sleep(2 * time.Second)
    
    // 测试HTTP代理
    client := &http.Client{
        Transport: &http.Transport{
            Proxy: http.ProxyURL(&url.URL{
                Scheme: "http",
                Host:   "127.0.0.1:7890",
            }),
        },
        Timeout: 10 * time.Second,
    }
    
    resp, err := client.Get("http://httpbin.org/ip")
    assert.NoError(t, err)
    assert.Equal(t, http.StatusOK, resp.StatusCode)
    
    defer resp.Body.Close()
}

func TestE2E_RuleMatching(t *testing.T) {
    tests := []struct {
        name     string
        target   string
        expected string
    }{
        {
            name:     "direct rule",
            target:   "www.baidu.com",
            expected: "DIRECT",
        },
        {
            name:     "proxy rule",
            target:   "www.google.com",
            expected: "PROXY",
        },
    }
    
    for _, tt := range tests {
        t.Run(tt.name, func(t *testing.T) {
            // 通过API查询规则匹配结果
            resp, err := http.Get(fmt.Sprintf("http://localhost:9090/rules/test?target=%s", tt.target))
            assert.NoError(t, err)
            defer resp.Body.Close()
            
            var result map[string]string
            err = json.NewDecoder(resp.Body).Decode(&result)
            assert.NoError(t, err)
            assert.Equal(t, tt.expected, result["adapter"])
        })
    }
}
```

### 3. Docker测试环境

```yaml
# test/docker-compose.yml
version: '3.8'

services:
  mihomo:
    build:
      context: ..
      dockerfile: test/Dockerfile
    ports:
      - "7890:7890"
      - "9090:9090"
    volumes:
      - ./config:/etc/mihomo
    depends_on:
      - mock-server
  
  mock-server:
    image: nginx:alpine
    ports:
      - "8080:80"
    volumes:
      - ./mock:/usr/share/nginx/html
  
  test-runner:
    build:
      context: ..
      dockerfile: test/Dockerfile.test
    depends_on:
      - mihomo
    command: go test -v ./test/...
```

## 性能测试

### 1. 压力测试

```go
// test/stress_test.go
func TestStress_ConcurrentConnections(t *testing.T) {
    if testing.Short() {
        t.Skip("skipping stress test in short mode")
    }
    
    const numConnections = 1000
    const duration = 30 * time.Second
    
    var wg sync.WaitGroup
    errors := make(chan error, numConnections)
    
    start := time.Now()
    for i := 0; i < numConnections; i++ {
        wg.Add(1)
        go func() {
            defer wg.Done()
            
            for time.Since(start) < duration {
                conn, err := net.Dial("tcp", "127.0.0.1:7890")
                if err != nil {
                    errors <- err
                    return
                }
                conn.Close()
                time.Sleep(10 * time.Millisecond)
            }
        }()
    }
    
    wg.Wait()
    close(errors)
    
    errorCount := 0
    for err := range errors {
        t.Logf("Connection error: %v", err)
        errorCount++
    }
    
    if errorCount > numConnections/10 { // 允许10%的错误率
        t.Fatalf("Too many connection errors: %d/%d", errorCount, numConnections)
    }
}
```

### 2. 内存泄漏测试

```go
func TestMemoryLeak_LongRunning(t *testing.T) {
    if testing.Short() {
        t.Skip("skipping memory leak test in short mode")
    }
    
    var m1, m2 runtime.MemStats
    runtime.GC()
    runtime.ReadMemStats(&m1)
    
    // 运行大量操作
    for i := 0; i < 10000; i++ {
        // 模拟正常操作
        metadata := &C.Metadata{
            Host:    "example.com",
            DstPort: "80",
        }
        _ = metadata.String()
    }
    
    runtime.GC()
    runtime.ReadMemStats(&m2)
    
    // 检查内存增长
    memGrowth := m2.Alloc - m1.Alloc
    if memGrowth > 1024*1024 { // 1MB
        t.Fatalf("Memory leak detected: %d bytes", memGrowth)
    }
}
```

## 调试技巧

### 1. 日志调试

```go
// 使用结构化日志
import "github.com/sirupsen/logrus"

func debugConnection(metadata *C.Metadata) {
    logrus.WithFields(logrus.Fields{
        "host":     metadata.Host,
        "port":     metadata.DstPort,
        "network":  metadata.NetWork,
        "source":   metadata.SrcIP,
    }).Debug("Processing connection")
}
```

### 2. pprof性能分析

```go
// 在main.go中添加pprof支持
import _ "net/http/pprof"

func init() {
    go func() {
        log.Println(http.ListenAndServe("localhost:6060", nil))
    }()
}
```

```bash
# 性能分析命令
go tool pprof http://localhost:6060/debug/pprof/profile
go tool pprof http://localhost:6060/debug/pprof/heap
go tool pprof http://localhost:6060/debug/pprof/goroutine
```

### 3. 调试工具

```bash
# 使用delve调试器
go install github.com/go-delve/delve/cmd/dlv@latest

# 调试运行中的程序
dlv attach $(pidof mihomo)

# 调试测试
dlv test -- -test.run TestVmess_DialContext
```

## 贡献指南

### 1. 开发流程

```bash
# 1. Fork项目并克隆
git clone https://github.com/your-username/mihomo.git
cd mihomo

# 2. 创建功能分支
git checkout -b feature/new-protocol

# 3. 开发和测试
make test
make lint

# 4. 提交更改
git add .
git commit -m "feat: add new protocol support"

# 5. 推送分支
git push origin feature/new-protocol

# 6. 创建Pull Request
```

### 2. 代码质量检查

```bash
# 运行所有检查
make check

# 单独运行检查
make lint        # 代码规范检查
make test        # 单元测试
make test-race   # 竞态检测
make coverage    # 测试覆盖率
```

### 3. 提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动

示例:
feat(adapter): add hysteria2 protocol support
fix(dns): resolve memory leak in cache
docs(wiki): update configuration guide
```

## 发布流程

### 1. 版本管理

```bash
# 创建发布分支
git checkout -b release/v1.15.0

# 更新版本号
echo "v1.15.0" > VERSION

# 更新CHANGELOG
vim CHANGELOG.md

# 提交版本更新
git commit -am "chore: bump version to v1.15.0"
```

### 2. 自动化构建

```yaml
# .github/workflows/release.yml
name: Release

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - uses: actions/setup-go@v3
      with:
        go-version: '1.20'
    
    - name: Build
      run: make build-all
    
    - name: Test
      run: make test
    
    - name: Release
      uses: goreleaser/goreleaser-action@v4
      with:
        args: release --rm-dist
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
```

### 3. 质量保证

```bash
# 发布前检查清单
echo "Release Checklist:"
echo "[ ] All tests pass"
echo "[ ] Documentation updated"
echo "[ ] CHANGELOG updated"
echo "[ ] Version bumped"
echo "[ ] Security scan passed"
echo "[ ] Performance regression test passed"

# 自动化检查脚本
#!/bin/bash
set -e

echo "Running pre-release checks..."

# 测试
make test
echo "✓ Tests passed"

# 代码质量
make lint
echo "✓ Linting passed"

# 安全扫描
make security-scan
echo "✓ Security scan passed"

# 构建检查
make build-all
echo "✓ Build successful"

echo "All checks passed! Ready for release."
```

通过这套完整的测试和开发体系，确保了Mihomo项目的代码质量和稳定性，为开源社区提供了可靠的网络代理解决方案。

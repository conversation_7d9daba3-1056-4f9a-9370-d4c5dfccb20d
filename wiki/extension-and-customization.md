# 扩展和自定义

## 目录
- [概述](#概述)
- [添加新协议](#添加新协议)
- [自定义规则类型](#自定义规则类型)
- [扩展DNS功能](#扩展dns功能)
- [插件系统](#插件系统)
- [外部工具集成](#外部工具集成)
- [构建标签和特性](#构建标签和特性)
- [开发指南](#开发指南)

## 概述

Mihomo采用模块化和插件化的架构设计，支持多种方式的扩展和自定义。开发者可以通过实现标准接口来添加新的协议支持、规则类型或其他功能模块。

## 添加新协议

### 1. 实现代理适配器接口

要添加新的代理协议，需要实现`C.ProxyAdapter`接口：

```go
// 在 adapter/outbound/ 目录下创建新协议文件
package outbound

import (
    "context"
    "net"
    C "github.com/metacubex/mihomo/constant"
)

type NewProtocol struct {
    *Base
    // 协议特定字段
    server   string
    port     int
    password string
}

type NewProtocolOption struct {
    BasicOption
    Name     string `proxy:"name"`
    Server   string `proxy:"server"`
    Port     int    `proxy:"port"`
    Password string `proxy:"password"`
}

// 实现必需的接口方法
func (n *NewProtocol) DialContext(ctx context.Context, metadata *C.Metadata) (C.Conn, error) {
    // 实现连接逻辑
    conn, err := net.Dial("tcp", net.JoinHostPort(n.server, strconv.Itoa(n.port)))
    if err != nil {
        return nil, err
    }
    
    // 协议握手和认证
    if err := n.handshake(conn); err != nil {
        conn.Close()
        return nil, err
    }
    
    return NewConn(conn, n), nil
}

func (n *NewProtocol) ListenPacketContext(ctx context.Context, metadata *C.Metadata) (C.PacketConn, error) {
    // 实现UDP支持（如果需要）
    return nil, fmt.Errorf("UDP not supported")
}

// 协议特定的握手逻辑
func (n *NewProtocol) handshake(conn net.Conn) error {
    // 实现协议握手
    return nil
}
```

### 2. 注册协议构造函数

在`adapter/outbound/parser.go`中注册新协议：

```go
func init() {
    // 注册新协议的构造函数
    RegisterOutboundAdapter("newprotocol", NewNewProtocolAdapter)
}

func NewNewProtocolAdapter(option map[string]any) (C.ProxyAdapter, error) {
    var opt NewProtocolOption
    if err := decoder.Decode(option, &opt); err != nil {
        return nil, err
    }
    
    return &NewProtocol{
        Base:     NewBase(opt.BasicOption),
        server:   opt.Server,
        port:     opt.Port,
        password: opt.Password,
    }, nil
}
```

### 3. 添加协议常量

在`constant/adapters.go`中添加新协议类型：

```go
const (
    // 现有协议...
    NewProtocolType AdapterType = iota + 100 // 使用新的数值
)

func (at AdapterType) String() string {
    switch at {
    // 现有case...
    case NewProtocolType:
        return "NewProtocol"
    default:
        return "Unknown"
    }
}
```

### 4. 传输层实现

如果协议需要特殊的传输层处理，在`transport/`目录下创建相应模块：

```go
// transport/newprotocol/client.go
package newprotocol

import (
    "net"
    "io"
)

type Client struct {
    config *Config
}

type Config struct {
    Server   string
    Port     int
    Password string
}

func (c *Client) StreamConn(conn net.Conn, metadata *Metadata) (net.Conn, error) {
    // 实现协议特定的流处理
    return &Conn{
        Conn:   conn,
        config: c.config,
    }, nil
}

type Conn struct {
    net.Conn
    config *Config
}

func (c *Conn) Read(b []byte) (n int, err error) {
    // 实现协议特定的读取逻辑
    return c.Conn.Read(b)
}

func (c *Conn) Write(b []byte) (n int, err error) {
    // 实现协议特定的写入逻辑
    return c.Conn.Write(b)
}
```

## 自定义规则类型

### 1. 实现规则接口

在`rules/common/`目录下创建新规则类型：

```go
// rules/common/custom_rule.go
package common

import (
    C "github.com/metacubex/mihomo/constant"
)

type CustomRule struct {
    *Base
    payload string
    target  string
    // 规则特定字段
}

func (r *CustomRule) RuleType() C.RuleType {
    return C.CustomRuleType // 需要在constant中定义
}

func (r *CustomRule) Match(metadata *C.Metadata) (bool, string) {
    // 实现匹配逻辑
    if r.matchCondition(metadata) {
        return true, r.target
    }
    return false, ""
}

func (r *CustomRule) Adapter() string {
    return r.target
}

func (r *CustomRule) Payload() string {
    return r.payload
}

func (r *CustomRule) matchCondition(metadata *C.Metadata) bool {
    // 实现具体的匹配条件
    return false
}

func NewCustomRule(payload, target string) (*CustomRule, error) {
    return &CustomRule{
        Base:    &Base{},
        payload: payload,
        target:  target,
    }, nil
}
```

### 2. 注册规则解析器

在`rules/parser.go`中添加新规则类型的解析：

```go
func ParseRule(tp, payload, target string, params []string, subRules map[string][]C.Rule) (parsed C.Rule, parseErr error) {
    switch tp {
    // 现有规则类型...
    case "CUSTOM-RULE":
        parsed, parseErr = RC.NewCustomRule(payload, target)
    // 其他case...
    }
    
    return
}
```

### 3. 添加规则常量

在`constant/rule.go`中定义新规则类型：

```go
type RuleType int

const (
    // 现有规则类型...
    CustomRuleType RuleType = iota + 100
)

func (rt RuleType) String() string {
    switch rt {
    // 现有case...
    case CustomRuleType:
        return "CustomRule"
    default:
        return "Unknown"
    }
}
```

## 扩展DNS功能

### 1. 自定义DNS客户端

```go
// dns/custom_client.go
package dns

import (
    "context"
    D "github.com/miekg/dns"
)

type CustomDNSClient struct {
    server string
    // 客户端特定配置
}

func (c *CustomDNSClient) ExchangeContext(ctx context.Context, m *D.Msg) (*D.Msg, error) {
    // 实现自定义DNS查询逻辑
    client := &D.Client{
        Net: "tcp", // 或其他协议
    }
    
    return client.ExchangeContext(ctx, m, c.server)
}

func (c *CustomDNSClient) Address() string {
    return c.server
}

func NewCustomDNSClient(server string) dnsClient {
    return &CustomDNSClient{
        server: server,
    }
}
```

### 2. 自定义DNS中间件

```go
// dns/custom_middleware.go
package dns

import (
    "context"
    "github.com/metacubex/mihomo/context"
    D "github.com/miekg/dns"
)

func withCustomProcessing() middleware {
    return func(next handler) handler {
        return func(ctx *context.DNSContext, r *D.Msg) (*D.Msg, error) {
            // 预处理
            if shouldProcess(r) {
                r = preprocessRequest(r)
            }
            
            // 调用下一个处理器
            msg, err := next(ctx, r)
            if err != nil {
                return nil, err
            }
            
            // 后处理
            if shouldPostProcess(msg) {
                msg = postprocessResponse(msg)
            }
            
            return msg, nil
        }
    }
}

func shouldProcess(r *D.Msg) bool {
    // 判断是否需要处理
    return true
}

func preprocessRequest(r *D.Msg) *D.Msg {
    // 预处理请求
    return r
}

func postprocessResponse(msg *D.Msg) *D.Msg {
    // 后处理响应
    return msg
}
```

## 插件系统

### 1. 插件接口定义

```go
// plugin/interface.go
package plugin

import (
    "context"
    C "github.com/metacubex/mihomo/constant"
)

type Plugin interface {
    Name() string
    Version() string
    Init(config map[string]interface{}) error
    Start() error
    Stop() error
}

type ConnectionPlugin interface {
    Plugin
    OnConnection(ctx context.Context, metadata *C.Metadata) error
}

type DNSPlugin interface {
    Plugin
    OnDNSRequest(ctx context.Context, domain string) (string, error)
}
```

### 2. 插件管理器

```go
// plugin/manager.go
package plugin

import (
    "fmt"
    "sync"
)

type Manager struct {
    plugins map[string]Plugin
    mu      sync.RWMutex
}

var defaultManager = &Manager{
    plugins: make(map[string]Plugin),
}

func Register(plugin Plugin) error {
    defaultManager.mu.Lock()
    defer defaultManager.mu.Unlock()
    
    name := plugin.Name()
    if _, exists := defaultManager.plugins[name]; exists {
        return fmt.Errorf("plugin %s already registered", name)
    }
    
    defaultManager.plugins[name] = plugin
    return nil
}

func Get(name string) (Plugin, bool) {
    defaultManager.mu.RLock()
    defer defaultManager.mu.RUnlock()
    
    plugin, exists := defaultManager.plugins[name]
    return plugin, exists
}

func StartAll() error {
    defaultManager.mu.RLock()
    defer defaultManager.mu.RUnlock()
    
    for _, plugin := range defaultManager.plugins {
        if err := plugin.Start(); err != nil {
            return err
        }
    }
    return nil
}
```

## 外部工具集成

### 1. iptables集成

Mihomo支持与iptables集成实现透明代理：

```go
// component/iptables/iptables.go
package iptables

import (
    "github.com/coreos/go-iptables/iptables"
)

type Manager struct {
    ipt *iptables.IPTables
}

func NewManager() (*Manager, error) {
    ipt, err := iptables.New()
    if err != nil {
        return nil, err
    }
    
    return &Manager{ipt: ipt}, nil
}

func (m *Manager) SetupRedirect(port int) error {
    // 设置重定向规则
    rules := [][]string{
        {"-t", "nat", "-A", "OUTPUT", "-p", "tcp", "--dport", "80,443", "-j", "REDIRECT", "--to-port", fmt.Sprintf("%d", port)},
        {"-t", "nat", "-A", "OUTPUT", "-p", "tcp", "--dport", "80,443", "-j", "REDIRECT", "--to-port", fmt.Sprintf("%d", port)},
    }
    
    for _, rule := range rules {
        if err := m.ipt.Append("nat", "OUTPUT", rule[4:]...); err != nil {
            return err
        }
    }
    
    return nil
}
```

### 2. TUN接口集成

通过sing-tun库集成TUN接口：

```go
// listener/sing_tun/listener.go
package sing_tun

import (
    "github.com/metacubex/sing-tun"
    "github.com/metacubex/sing/common/logger"
)

type Listener struct {
    tun    tun.Tun
    stack  tun.Stack
    logger logger.Logger
}

func New(options tun.Options) (*Listener, error) {
    tunInterface, err := tun.New(options)
    if err != nil {
        return nil, err
    }
    
    stack, err := tun.NewStack(options.Stack, tun.StackOptions{
        Context: context.Background(),
        Tun:     tunInterface,
        Logger:  logger,
    })
    if err != nil {
        tunInterface.Close()
        return nil, err
    }
    
    return &Listener{
        tun:   tunInterface,
        stack: stack,
    }, nil
}
```

## 构建标签和特性

### 1. 构建标签

Mihomo使用构建标签来控制特性的编译：

```go
//go:build with_gvisor
// +build with_gvisor

package features

func init() {
    tags = append(tags, "with_gvisor")
}
```

### 2. 特性检测

```go
// constant/features/features.go
package features

var tags []string

func Tags() []string {
    return tags
}

func HasFeature(feature string) bool {
    for _, tag := range tags {
        if tag == feature {
            return true
        }
    }
    return false
}
```

### 3. 条件编译

```bash
# 编译时包含特定特性
go build -tags "with_gvisor,with_lwip" .

# 编译时排除特定特性
go build -tags "!with_gvisor" .
```

## 开发指南

### 1. 代码结构规范

- **接口优先**: 定义清晰的接口，便于测试和扩展
- **错误处理**: 使用Go标准的错误处理模式
- **并发安全**: 使用适当的同步原语保证线程安全
- **资源管理**: 及时释放资源，避免内存泄漏

### 2. 测试规范

```go
// adapter/outbound/newprotocol_test.go
package outbound

import (
    "testing"
    "context"
)

func TestNewProtocol_DialContext(t *testing.T) {
    // 创建测试实例
    proxy := &NewProtocol{
        Base:     NewBase(BasicOption{}),
        server:   "example.com",
        port:     8080,
        password: "test",
    }
    
    // 测试连接
    ctx := context.Background()
    metadata := &C.Metadata{
        Host: "target.com",
        DstPort: "80",
    }
    
    conn, err := proxy.DialContext(ctx, metadata)
    if err != nil {
        t.Fatalf("DialContext failed: %v", err)
    }
    defer conn.Close()
    
    // 验证连接
    if conn == nil {
        t.Fatal("Expected non-nil connection")
    }
}
```

### 3. 文档规范

- **接口文档**: 详细说明接口的用途和参数
- **示例代码**: 提供使用示例
- **配置说明**: 说明配置选项和默认值
- **兼容性**: 说明版本兼容性和变更

### 4. 性能考虑

- **内存池**: 使用对象池减少GC压力
- **零拷贝**: 在可能的情况下避免数据拷贝
- **批处理**: 批量处理提高效率
- **缓存**: 合理使用缓存减少重复计算

通过这些扩展机制，开发者可以根据具体需求定制Mihomo的功能，同时保持代码的整洁性和可维护性。

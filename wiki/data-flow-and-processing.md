# 数据流和处理

## 目录
- [概述](#概述)
  - [处理模型](#处理模型)
  - [核心组件](#核心组件)
  - [数据流架构](#数据流架构)
- [TCP连接处理流程](#tcp连接处理流程)
  - [连接接收阶段](#连接接收阶段)
  - [元数据提取阶段](#元数据提取阶段)
  - [规则匹配阶段](#规则匹配阶段)
  - [DNS解析阶段](#dns解析阶段)
  - [代理连接阶段](#代理连接阶段)
  - [数据转发阶段](#数据转发阶段)
  - [连接清理阶段](#连接清理阶段)
- [UDP包处理流程](#udp包处理流程)
  - [工作池模型](#工作池模型)
  - [包接收处理](#包接收处理)
  - [NAT表管理](#nat表管理)
  - [会话生命周期](#会话生命周期)
- [DNS解析流程](#dns解析流程)
  - [解析策略](#解析策略)
  - [缓存机制](#缓存机制)
  - [Fake IP处理](#fake-ip处理)
  - [上游选择](#上游选择)
- [规则匹配机制](#规则匹配机制)
  - [匹配算法](#匹配算法)
  - [规则优化](#规则优化)
  - [逻辑运算](#逻辑运算)
  - [动态规则](#动态规则)
- [代理选择逻辑](#代理选择逻辑)
  - [选择策略](#选择策略)
  - [健康检查](#健康检查)
  - [负载均衡](#负载均衡)
  - [故障转移](#故障转移)
- [并发处理模型](#并发处理模型)
  - [Goroutine管理](#goroutine管理)
  - [同步机制](#同步机制)
  - [资源池化](#资源池化)
  - [背压控制](#背压控制)
- [错误处理和恢复](#错误处理和恢复)
  - [错误分类](#错误分类)
  - [重试机制](#重试机制)
  - [降级策略](#降级策略)
  - [监控告警](#监控告警)
- [性能优化策略](#性能优化策略)
  - [内存优化](#内存优化)
  - [网络优化](#网络优化)
  - [CPU优化](#cpu优化)
  - [I/O优化](#io优化)

## 概述

Mihomo采用事件驱动的数据处理模型，通过多层处理管道实现高效的网络流量转发。系统支持TCP和UDP两种传输协议，并提供了完整的连接生命周期管理。

### 处理模型

#### 事件驱动架构

```mermaid
graph TB
    subgraph "事件源"
        A[客户端连接] --> E[事件总线]
        B[配置变更] --> E
        C[健康检查] --> E
        D[统计更新] --> E
    end

    subgraph "事件处理器"
        E --> F[连接处理器]
        E --> G[配置处理器]
        E --> H[监控处理器]
        E --> I[统计处理器]
    end

    subgraph "处理管道"
        F --> J[元数据提取]
        J --> K[规则匹配]
        K --> L[DNS解析]
        L --> M[代理选择]
        M --> N[连接建立]
        N --> O[数据转发]
    end
```

#### 异步处理流水线

- **非阻塞I/O**: 使用Go的goroutine实现异步处理
- **管道模式**: 数据在各个处理阶段间流动
- **背压控制**: 通过channel缓冲控制处理速度
- **错误隔离**: 单个连接错误不影响其他连接

### 核心组件

#### 隧道核心 (`tunnel/tunnel.go`)

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
type tunnel struct{}

var Tunnel = tunnel{}

// HandleTCPConn 处理TCP连接的主入口
func (t tunnel) HandleTCPConn(conn net.Conn, metadata *C.Metadata) {
    connCtx := icontext.NewConnContext(conn, metadata)
    handleTCPConn(connCtx)
}

// HandleUDPPacket 处理UDP包的主入口
func (t tunnel) HandleUDPPacket(packet C.UDPPacket, metadata *C.Metadata) {
    udpInit.Do(initUDP)

    packetAdapter := C.NewPacketAdapter(packet, metadata)
    key := packetAdapter.Key()

    hash := utils.MapHash(key)
    queueNo := uint(hash) % uint(len(udpQueues))

    select {
    case udpQueues[queueNo] <- packetAdapter:
    default:
        packet.Drop()
    }
}
````
</augment_code_snippet>

#### 连接上下文 (`context/conn.go`)

<augment_code_snippet path="context/conn.go" mode="EXCERPT">
````go
type ConnContext struct {
    id       uuid.UUID
    metadata *C.Metadata
    conn     *N.BufferedConn
}

func NewConnContext(conn net.Conn, metadata *C.Metadata) *ConnContext {
    return &ConnContext{
        id:       utils.NewUUIDV4(),
        metadata: metadata,
        conn:     N.NewBufferedConn(conn),
    }
}
````
</augment_code_snippet>

### 数据流架构

#### 整体数据流图

```mermaid
flowchart TD
    subgraph "入站层"
        A[客户端] --> B[监听器]
        B --> C[协议解析器]
        C --> D[元数据提取器]
    end

    subgraph "处理层"
        D --> E[规则引擎]
        E --> F{规则匹配结果}
        F -->|DIRECT| G[直连处理]
        F -->|PROXY| H[代理处理]
        F -->|REJECT| I[拒绝处理]
    end

    subgraph "解析层"
        H --> J{需要DNS解析?}
        J -->|是| K[DNS解析器]
        J -->|否| L[代理选择器]
        K --> L
    end

    subgraph "出站层"
        G --> M[目标服务器]
        L --> N[代理服务器]
        N --> M
        I --> O[连接关闭]
    end

    subgraph "监控层"
        P[统计收集器] --> Q[API接口]
        Q --> R[Web控制台]
    end

    D --> P
    E --> P
    L --> P
    M --> P
```

#### 数据包生命周期

```mermaid
stateDiagram-v2
    [*] --> Received: 包到达
    Received --> Parsed: 协议解析
    Parsed --> Validated: 元数据验证
    Validated --> Matched: 规则匹配
    Matched --> Resolved: DNS解析(可选)
    Resolved --> Selected: 代理选择
    Selected --> Connected: 连接建立
    Connected --> Forwarded: 数据转发
    Forwarded --> Closed: 连接关闭
    Closed --> [*]

    Validated --> Dropped: 验证失败
    Matched --> Rejected: 规则拒绝
    Selected --> Failed: 连接失败
    Dropped --> [*]
    Rejected --> [*]
    Failed --> [*]
```

## TCP连接处理流程

### 连接接收阶段

#### 监听器架构

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Listener as 监听器
    participant Tunnel as 隧道核心
    participant Context as 连接上下文
    participant Handler as 处理器

    Client->>Listener: TCP连接请求
    Listener->>Listener: Accept()接受连接
    Listener->>Context: 创建连接上下文
    Context->>Tunnel: HandleTCPConn()
    Tunnel->>Handler: 启动处理goroutine
    Handler->>Handler: handleTCPConn()
```

#### 监听器实现

不同协议的监听器实现统一的接口：

```go
// HTTP监听器处理
func (l *Listener) handleConn(conn net.Conn) {
    // 解析HTTP CONNECT请求
    br := bufio.NewReader(conn)
    request, err := http.ReadRequest(br)
    if err != nil {
        conn.Close()
        return
    }

    // 提取目标地址
    host := request.Host
    if host == "" {
        host = request.URL.Host
    }

    // 创建元数据
    metadata := &C.Metadata{
        Type:    C.HTTP,
        NetWork: C.TCP,
        Host:    host,
        DstPort: 80, // 默认端口
    }

    // 发送到隧道处理
    l.tunnel.HandleTCPConn(conn, metadata)
}
```

#### SOCKS监听器处理

```go
func (l *Listener) handleConn(conn net.Conn) {
    // SOCKS握手
    if err := socks5.ServerHandshake(conn, l.authenticator); err != nil {
        conn.Close()
        return
    }

    // 解析SOCKS请求
    target, command, err := socks5.ServerHandshake(conn)
    if err != nil {
        conn.Close()
        return
    }

    // 创建元数据
    metadata := &C.Metadata{
        Type:    C.SOCKS5,
        NetWork: C.TCP,
        Host:    target.String(),
        DstPort: uint16(target.Port),
    }

    l.tunnel.HandleTCPConn(conn, metadata)
}
```

### 元数据提取阶段

#### 元数据构建过程

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func handleTCPConn(connCtx C.ConnContext) {
    defer func(conn net.Conn) {
        _ = conn.Close()
    }(connCtx.Conn())

    metadata := connCtx.Metadata()
    if !metadata.Valid() {
        log.Warnln("[Metadata] not valid: %#v", metadata)
        return
    }

    // 修复元数据
    fixMetadata(metadata)

    // 预处理元数据
    if err := preHandleMetadata(metadata); err != nil {
        log.Debugln("[Metadata PreHandle] error: %s", err)
        return
    }
}
````
</augment_code_snippet>

#### 元数据验证和修复

```go
func fixMetadata(metadata *C.Metadata) {
    // 修复源地址信息
    if metadata.SrcIP.IsUnspecified() && metadata.RawSrcAddr != nil {
        if addr, ok := metadata.RawSrcAddr.(*net.TCPAddr); ok {
            metadata.SrcIP = netip.AddrFrom16(addr.IP.To16())
            metadata.SrcPort = uint16(addr.Port)
        }
    }

    // 修复目标地址信息
    if metadata.DstIP.IsUnspecified() && metadata.Host != "" {
        if ip, err := netip.ParseAddr(metadata.Host); err == nil {
            metadata.DstIP = ip
        }
    }

    // 设置默认端口
    if metadata.DstPort == 0 {
        switch metadata.Type {
        case C.HTTP:
            metadata.DstPort = 80
        case C.HTTPS:
            metadata.DstPort = 443
        case C.SOCKS5:
            metadata.DstPort = 1080
        }
    }
}

func preHandleMetadata(metadata *C.Metadata) error {
    // 进程信息获取
    if findProcessMode != P.FindProcessDisabled {
        if err := findProcess(metadata); err != nil {
            log.Debugln("[Process] find process %s error: %v", metadata.String(), err)
        }
    }

    // 地理位置信息
    if geoIPEnable {
        metadata.DstGeoIP = geoip.LookupCountry(metadata.DstIP)
        metadata.SrcGeoIP = geoip.LookupCountry(metadata.SrcIP)
    }

    // ASN信息
    if asnEnable {
        metadata.DstIPASN = asn.LookupASN(metadata.DstIP)
        metadata.SrcIPASN = asn.LookupASN(metadata.SrcIP)
    }

    return nil
}
```

### 规则匹配阶段

#### 规则匹配流程

```mermaid
flowchart TD
    A[开始规则匹配] --> B[获取规则列表]
    B --> C[遍历规则]
    C --> D{规则匹配?}
    D -->|否| E{还有规则?}
    D -->|是| F[返回匹配结果]
    E -->|是| C
    E -->|否| G[使用默认规则]
    F --> H[记录匹配规则]
    G --> H
    H --> I[结束匹配]
```

#### 规则匹配实现

```go
func match(metadata *C.Metadata) (C.Proxy, C.Rule, error) {
    // 创建规则匹配辅助器
    helper := C.RuleMatchHelper{
        ResolveIP: func() {
            if metadata.DstIP.IsUnspecified() && metadata.Host != "" {
                ip, err := resolver.ResolveIP(metadata.Host)
                if err == nil {
                    metadata.DstIP = ip
                }
            }
        },
        FindProcess: func() {
            if metadata.Process == "" {
                process, err := findProcessByConnection(metadata)
                if err == nil {
                    metadata.Process = process
                }
            }
        },
    }

    // 遍历规则
    for _, rule := range rules {
        if matched, adapter := rule.Match(metadata, helper); matched {
            proxy := proxies[adapter]
            if proxy == nil {
                return nil, rule, fmt.Errorf("proxy %s not found", adapter)
            }
            return proxy, rule, nil
        }
    }

    // 默认规则
    return proxies["DIRECT"], nil, nil
}
```

#### 规则类型匹配

```go
// 域名规则匹配
func (r *Domain) Match(metadata *C.Metadata, helper C.RuleMatchHelper) (bool, string) {
    return metadata.Host == r.domain, r.adapter
}

// IP CIDR规则匹配
func (r *IPCIDR) Match(metadata *C.Metadata, helper C.RuleMatchHelper) (bool, string) {
    helper.ResolveIP()
    return r.ipNet.Contains(metadata.DstIP.AsSlice()), r.adapter
}

// 进程规则匹配
func (r *Process) Match(metadata *C.Metadata, helper C.RuleMatchHelper) (bool, string) {
    helper.FindProcess()
    return metadata.Process == r.process, r.adapter
}

// 逻辑AND规则匹配
func (r *LogicAND) Match(metadata *C.Metadata, helper C.RuleMatchHelper) (bool, string) {
    for _, rule := range r.rules {
        if matched, _ := rule.Match(metadata, helper); !matched {
            return false, ""
        }
    }
    return true, r.adapter
}
```

### DNS解析阶段

#### DNS解析决策

```mermaid
flowchart TD
    A[需要DNS解析?] --> B{目标是域名?}
    B -->|否| C[使用现有IP]
    B -->|是| D{Fake IP模式?}
    D -->|是| E[分配Fake IP]
    D -->|否| F[真实DNS解析]
    E --> G[记录域名映射]
    F --> H{解析成功?}
    H -->|是| I[更新元数据]
    H -->|否| J[使用缓存或失败]
    G --> K[继续处理]
    I --> K
    J --> K
    C --> K
```

#### DNS解析实现

```go
func resolveMetadata(metadata *C.Metadata) error {
    // 如果已有IP地址，无需解析
    if !metadata.DstIP.IsUnspecified() {
        return nil
    }

    // 检查是否为域名
    if metadata.Host == "" {
        return errors.New("no host to resolve")
    }

    // Fake IP模式处理
    if fakeIPEnabled && shouldUseFakeIP(metadata.Host) {
        fakeIP := fakeIPPool.Lookup(metadata.Host)
        metadata.DstIP = fakeIP
        metadata.DNSMode = C.DNSFakeIP
        return nil
    }

    // 真实DNS解析
    ctx, cancel := context.WithTimeout(context.Background(), dnsTimeout)
    defer cancel()

    ips, err := resolver.LookupIP(ctx, metadata.Host)
    if err != nil {
        return fmt.Errorf("DNS resolve failed: %w", err)
    }

    // 选择合适的IP地址
    var selectedIP netip.Addr
    for _, ip := range ips {
        if ipv6Enabled || ip.Is4() {
            selectedIP = ip
            break
        }
    }

    if selectedIP.IsUnspecified() {
        return errors.New("no suitable IP found")
    }

    metadata.DstIP = selectedIP
    metadata.DNSMode = C.DNSNormal
    return nil
}
```

### 代理连接阶段

#### 代理选择和连接

```go
func dialProxy(proxy C.Proxy, metadata *C.Metadata) (C.Conn, error) {
    // 创建连接上下文
    ctx, cancel := context.WithTimeout(context.Background(), dialTimeout)
    defer cancel()

    // 通过代理建立连接
    conn, err := proxy.DialContext(ctx, metadata)
    if err != nil {
        return nil, fmt.Errorf("dial proxy failed: %w", err)
    }

    // 连接后处理
    if tcpConn, ok := conn.(*net.TCPConn); ok {
        // 设置TCP选项
        tcpConn.SetKeepAlive(true)
        tcpConn.SetKeepAlivePeriod(keepAlivePeriod)
        tcpConn.SetNoDelay(true)
    }

    return conn, nil
}
```

### 数据转发阶段

#### 双向数据转发

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func relay(leftConn, rightConn net.Conn) {
    ch := make(chan error, 2)

    go func() {
        // 左到右的数据转发
        _, err := io.Copy(rightConn, leftConn)
        rightConn.SetDeadline(time.Now())
        leftConn.SetDeadline(time.Now())
        ch <- err
    }()

    go func() {
        // 右到左的数据转发
        _, err := io.Copy(leftConn, rightConn)
        rightConn.SetDeadline(time.Now())
        leftConn.SetDeadline(time.Now())
        ch <- err
    }()

    // 等待任一方向完成
    <-ch
}
````
</augment_code_snippet>

#### 缓冲优化转发

```go
func relayWithBuffer(leftConn, rightConn net.Conn) {
    // 使用缓冲池
    buf1 := pool.Get(pool.RelayBufferSize)
    buf2 := pool.Get(pool.RelayBufferSize)
    defer pool.Put(buf1)
    defer pool.Put(buf2)

    ch := make(chan error, 2)

    go func() {
        err := copyBuffer(rightConn, leftConn, buf1)
        ch <- err
    }()

    go func() {
        err := copyBuffer(leftConn, rightConn, buf2)
        ch <- err
    }()

    <-ch
}

func copyBuffer(dst io.Writer, src io.Reader, buf []byte) error {
    for {
        nr, er := src.Read(buf)
        if nr > 0 {
            nw, ew := dst.Write(buf[0:nr])
            if ew != nil {
                return ew
            }
            if nr != nw {
                return io.ErrShortWrite
            }
        }
        if er != nil {
            if er != io.EOF {
                return er
            }
            break
        }
    }
    return nil
}
```

### 连接清理阶段

#### 资源清理

```go
func cleanupConnection(connCtx C.ConnContext, proxy C.Proxy, remoteConn net.Conn) {
    // 关闭连接
    if connCtx.Conn() != nil {
        connCtx.Conn().Close()
    }
    if remoteConn != nil {
        remoteConn.Close()
    }

    // 更新统计
    statistic.DefaultManager.PushClosed(connCtx.ID())

    // 记录连接信息
    log.Infoln("[TCP] %s --> %s closed",
        connCtx.Metadata().SourceAddress(),
        connCtx.Metadata().RemoteAddress())
}
```

## UDP包处理流程

### 工作池模型

#### UDP工作池初始化

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func initUDP() {
    numUDPWorkers := 4
    if num := runtime.GOMAXPROCS(0); num > numUDPWorkers {
        numUDPWorkers = num
    }

    udpQueues = make([]chan C.PacketAdapter, numUDPWorkers)
    for i := 0; i < numUDPWorkers; i++ {
        queue := make(chan C.PacketAdapter, queueCapacity)
        udpQueues[i] = queue
        go processUDP(queue)
    }
}
````
</augment_code_snippet>

#### 工作池架构

```mermaid
graph TB
    subgraph "UDP包接收"
        A[UDP监听器] --> B[包适配器]
        B --> C[哈希计算]
    end

    subgraph "工作队列分发"
        C --> D[队列0<br/>Worker 0]
        C --> E[队列1<br/>Worker 1]
        C --> F[队列2<br/>Worker 2]
        C --> G[队列N<br/>Worker N]
    end

    subgraph "包处理"
        D --> H[handleUDPConn]
        E --> H
        F --> H
        G --> H
    end

    subgraph "NAT管理"
        H --> I[NAT表查询]
        I --> J{会话存在?}
        J -->|是| K[复用会话]
        J -->|否| L[创建会话]
    end
```

#### 负载均衡策略

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func (t tunnel) HandleUDPPacket(packet C.UDPPacket, metadata *C.Metadata) {
    udpInit.Do(initUDP)

    packetAdapter := C.NewPacketAdapter(packet, metadata)
    key := packetAdapter.Key()

    // 基于连接键值的哈希分发
    hash := utils.MapHash(key)
    queueNo := uint(hash) % uint(len(udpQueues))

    select {
    case udpQueues[queueNo] <- packetAdapter:
        // 成功加入队列
    default:
        // 队列满，丢弃包
        packet.Drop()
    }
}
````
</augment_code_snippet>

### 包接收处理

#### UDP包处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Listener as UDP监听器
    participant Queue as 工作队列
    participant Worker as 工作协程
    participant NAT as NAT表
    participant Proxy as 代理
    participant Target as 目标服务器

    Client->>Listener: UDP包
    Listener->>Queue: 包适配器
    Queue->>Worker: 取出包
    Worker->>Worker: 验证元数据
    Worker->>NAT: 查询会话

    alt 会话不存在
        Worker->>NAT: 创建新会话
        Worker->>Proxy: 建立代理连接
        Proxy->>Target: 连接目标
    else 会话存在
        Worker->>NAT: 获取现有会话
    end

    Worker->>Proxy: 发送UDP包
    Proxy->>Target: 转发包
    Target->>Proxy: 响应包
    Proxy->>Worker: 返回响应
    Worker->>Client: 转发响应
```

#### 包处理实现

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func handleUDPConn(packet C.PacketAdapter) {
    if !isHandle(packet.Metadata().Type) {
        packet.Drop()
        return
    }

    metadata := packet.Metadata()
    if !metadata.Valid() {
        packet.Drop()
        log.Warnln("[Metadata] not valid: %#v", metadata)
        return
    }

    // 修复元数据
    fixMetadata(metadata)

    // 预处理元数据
    if err := preHandleMetadata(metadata.Clone()); err != nil {
        packet.Drop()
        log.Debugln("[Metadata PreHandle] error: %s", err)
        return
    }

    key := packet.Key()
    sender, loaded := natTable.GetOrCreate(key, func() C.PacketSender {
        sender := newPacketSender()
        if sniffingEnable && snifferDispatcher.Enable() {
            return snifferDispatcher.UDPSniff(packet, sender)
        }
        return sender
    })

    if !loaded {
        // 新会话，需要建立连接
        go func() {
            pc, proxy, err := dial()
            if err != nil {
                sender.Close()
                natTable.Delete(key)
                return
            }
            sender.Process(pc, proxy)
        }()
    }

    sender.Send(packet) // 非阻塞发送
}
````
</augment_code_snippet>

### NAT表管理

#### NAT表结构

```go
type NatTable interface {
    Set(key string, pc C.PacketConn)
    Get(key string) C.PacketConn
    GetOrCreate(key string, create func() C.PacketSender) (C.PacketSender, bool)
    Delete(key string)
    DeleteByConn(conn C.PacketConn)
    Range(fn func(key string, value C.PacketConn) bool)
    Size() int
}

type natTable struct {
    mapping sync.Map
    janitor *janitor
}

type natEntry struct {
    PacketSender C.PacketSender
    CreatedAt    time.Time
    LastActive   time.Time
}
```

#### NAT表操作

```go
func (nt *natTable) GetOrCreate(key string, create func() C.PacketSender) (C.PacketSender, bool) {
    if entry, loaded := nt.mapping.Load(key); loaded {
        natEntry := entry.(*natEntry)
        natEntry.LastActive = time.Now()
        return natEntry.PacketSender, true
    }

    // 创建新的发送器
    sender := create()
    entry := &natEntry{
        PacketSender: sender,
        CreatedAt:    time.Now(),
        LastActive:   time.Now(),
    }

    nt.mapping.Store(key, entry)
    return sender, false
}

func (nt *natTable) cleanup() {
    now := time.Now()
    nt.mapping.Range(func(key, value interface{}) bool {
        entry := value.(*natEntry)
        if now.Sub(entry.LastActive) > udpTimeout {
            entry.PacketSender.Close()
            nt.mapping.Delete(key)
        }
        return true
    })
}
```

#### 会话超时管理

```go
type janitor struct {
    interval time.Duration
    stop     chan struct{}
}

func newJanitor(interval time.Duration, table *natTable) *janitor {
    j := &janitor{
        interval: interval,
        stop:     make(chan struct{}),
    }

    go func() {
        ticker := time.NewTicker(interval)
        defer ticker.Stop()

        for {
            select {
            case <-ticker.C:
                table.cleanup()
            case <-j.stop:
                return
            }
        }
    }()

    return j
}
```

### 会话生命周期

#### 包发送器实现

```go
type PacketSender interface {
    Send(packet C.PacketAdapter)
    Process(pc C.PacketConn, proxy C.Proxy)
    Close()
}

type packetSender struct {
    ch     chan C.PacketAdapter
    conn   C.PacketConn
    proxy  C.Proxy
    closed atomic.Bool
}

func newPacketSender() *packetSender {
    return &packetSender{
        ch: make(chan C.PacketAdapter, senderQueueSize),
    }
}

func (ps *packetSender) Send(packet C.PacketAdapter) {
    if ps.closed.Load() {
        packet.Drop()
        return
    }

    select {
    case ps.ch <- packet:
    default:
        // 队列满，丢弃包
        packet.Drop()
    }
}

func (ps *packetSender) Process(pc C.PacketConn, proxy C.Proxy) {
    ps.conn = pc
    ps.proxy = proxy

    // 启动发送循环
    go ps.sendLoop()

    // 启动接收循环
    go ps.receiveLoop()
}

func (ps *packetSender) sendLoop() {
    for packet := range ps.ch {
        if ps.closed.Load() {
            packet.Drop()
            continue
        }

        _, err := ps.conn.WriteTo(packet.Data(), packet.Metadata().UDPAddr())
        if err != nil {
            log.Debugln("[UDP] write to %s error: %v", packet.Metadata().RemoteAddress(), err)
        }

        // 更新统计
        statistic.DefaultManager.PushUploaded(packet.Data())
    }
}

func (ps *packetSender) receiveLoop() {
    buf := pool.Get(pool.UDPBufferSize)
    defer pool.Put(buf)

    for {
        if ps.closed.Load() {
            break
        }

        n, addr, err := ps.conn.ReadFrom(buf)
        if err != nil {
            if !ps.closed.Load() {
                log.Debugln("[UDP] read from %s error: %v", addr, err)
            }
            break
        }

        // 创建响应包
        data := make([]byte, n)
        copy(data, buf[:n])

        // 发送回客户端
        ps.sendToClient(data, addr)

        // 更新统计
        statistic.DefaultManager.PushDownloaded(data)
    }
}
```

#### UDP会话状态机

```mermaid
stateDiagram-v2
    [*] --> Creating: 收到首个包
    Creating --> Active: 连接建立成功
    Creating --> Failed: 连接建立失败
    Active --> Active: 包传输
    Active --> Idle: 无活动
    Idle --> Active: 收到新包
    Idle --> Timeout: 超时
    Active --> Closed: 主动关闭
    Failed --> [*]
    Timeout --> [*]
    Closed --> [*]
```

#### 会话清理机制

```go
func (ps *packetSender) Close() {
    if ps.closed.CompareAndSwap(false, true) {
        close(ps.ch)

        if ps.conn != nil {
            ps.conn.Close()
        }

        // 清理缓冲的包
        for packet := range ps.ch {
            packet.Drop()
        }
    }
}
```
func initUDP() {
    numUDPWorkers := 4
    if num := runtime.GOMAXPROCS(0); num > numUDPWorkers {
        numUDPWorkers = num
    }

    udpQueues = make([]chan C.PacketAdapter, numUDPWorkers)
    for i := 0; i < numUDPWorkers; i++ {
        queue := make(chan C.PacketAdapter, queueCapacity)
        udpQueues[i] = queue
        go processUDP(queue)
    }
}
````
</augment_code_snippet>

### 2. 包分发机制

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func (t tunnel) HandleUDPPacket(packet C.UDPPacket, metadata *C.Metadata) {
    udpInit.Do(initUDP)

    packetAdapter := C.NewPacketAdapter(packet, metadata)
    key := packetAdapter.Key()

    hash := utils.MapHash(key)
    queueNo := uint(hash) % uint(len(udpQueues))

    select {
    case udpQueues[queueNo] <- packetAdapter:
    default:
        packet.Drop()
    }
}
````
</augment_code_snippet>

### 3. NAT表管理

UDP连接使用NAT表进行会话管理：

```mermaid
graph TB
    A[UDP包到达] --> B[计算包键值]
    B --> C{NAT表中存在?}
    C -->|是| D[使用现有会话]
    C -->|否| E[创建新会话]
    E --> F[添加到NAT表]
    D --> G[转发包]
    F --> G
```

## DNS解析流程

### 1. DNS解析器架构

<augment_code_snippet path="dns/resolver.go" mode="EXCERPT">
````go
type Resolver struct {
    ipv6                  bool
    ipv6Timeout           time.Duration
    hosts                 *trie.DomainTrie[resolver.HostValue]
    main                  []dnsClient
    fallback              []dnsClient
    fallbackDomainFilters []C.DomainMatcher
    fallbackIPFilters     []C.IpMatcher
    group                 singleflight.Group[*D.Msg]
    cache                 dnsCache
    policy                []dnsPolicy
    defaultResolver       *Resolver
}
````
</augment_code_snippet>

### 2. DNS查询流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Cache as DNS缓存
    participant Main as 主DNS
    participant Fallback as 备用DNS
    
    Client->>Cache: 查询域名
    alt 缓存命中
        Cache->>Client: 返回缓存结果
    else 缓存未命中
        Cache->>Main: 查询主DNS
        alt 主DNS成功
            Main->>Cache: 返回结果
            Cache->>Client: 返回结果
        else 主DNS失败
            Cache->>Fallback: 查询备用DNS
            Fallback->>Cache: 返回结果
            Cache->>Client: 返回结果
        end
    end
```

### 3. Fake IP模式

在Fake IP模式下，DNS解析器返回虚假IP地址，实际解析在连接时进行：

1. **虚假IP分配**: 为域名分配虚假IP地址
2. **映射存储**: 维护虚假IP到真实域名的映射
3. **延迟解析**: 在建立连接时进行真实DNS解析

## 规则匹配机制

### 1. 规则类型和优先级

<augment_code_snippet path="rules/parser.go" mode="EXCERPT">
````go
switch tp {
case "DOMAIN":
    parsed = RC.NewDomain(payload, target)
case "DOMAIN-SUFFIX":
    parsed = RC.NewDomainSuffix(payload, target)
case "DOMAIN-KEYWORD":
    parsed = RC.NewDomainKeyword(payload, target)
case "DOMAIN-REGEX":
    parsed, parseErr = RC.NewDomainRegex(payload, target)
case "GEOSITE":
    parsed, parseErr = RC.NewGEOSITE(payload, target)
case "GEOIP":
    isSrc, noResolve := RC.ParseParams(params)
    parsed, parseErr = RC.NewGEOIP(payload, target, isSrc, noResolve)
````
</augment_code_snippet>

### 2. 规则匹配流程

```mermaid
graph TD
    A[开始匹配] --> B[获取元数据]
    B --> C[遍历规则列表]
    C --> D{规则匹配?}
    D -->|是| E[返回目标代理]
    D -->|否| F{还有规则?}
    F -->|是| C
    F -->|否| G[使用默认规则]
    G --> E
```

### 3. 逻辑规则处理

支持AND、OR、NOT等逻辑运算：

```yaml
rules:
  - AND,((DOMAIN,example.com),(DST-PORT,443)),PROXY
  - OR,((GEOIP,CN),(DOMAIN-SUFFIX,cn)),DIRECT
  - NOT,((DOMAIN-KEYWORD,ads)),REJECT
```

## 代理选择逻辑

### 1. 代理组策略

<augment_code_snippet path="adapter/outboundgroup/groupbase.go" mode="EXCERPT">
````go
func (gb *GroupBase) GetProxies(touch bool) []C.Proxy {
    if touch {
        defer gb.getProxiesMutex.Unlock()
        gb.getProxiesMutex.Lock()
    }

    var proxies []C.Proxy
    if len(gb.filterRegs) == 0 {
        for _, pd := range gb.providers {
            proxies = append(proxies, pd.Proxies()...)
        }
    }
````
</augment_code_snippet>

### 2. 健康检查机制

```mermaid
graph LR
    A[代理组] --> B[定期健康检查]
    B --> C{代理可用?}
    C -->|是| D[标记为可用]
    C -->|否| E[标记为不可用]
    D --> F[更新延迟信息]
    E --> G[从可用列表移除]
```

## 并发处理模型

### 1. Goroutine管理

- **连接处理**: 每个TCP连接使用独立的goroutine
- **UDP工作池**: 使用固定数量的worker goroutine处理UDP包
- **健康检查**: 独立的goroutine进行代理健康检查

### 2. 同步机制

```go
// 使用sync包提供的同步原语
type SafeMap struct {
    mu sync.RWMutex
    data map[string]interface{}
}

func (m *SafeMap) Get(key string) (interface{}, bool) {
    m.mu.RLock()
    defer m.mu.RUnlock()
    val, ok := m.data[key]
    return val, ok
}
```

### 3. 内存管理

- **对象池**: 使用sync.Pool复用频繁分配的对象
- **缓冲区管理**: 统一的缓冲区池管理
- **连接池**: HTTP客户端连接池复用

## 错误处理和恢复

### 1. 错误分类

- **网络错误**: 连接超时、连接拒绝等
- **协议错误**: 协议解析失败、格式错误等
- **配置错误**: 配置文件错误、规则错误等

### 2. 故障转移

```mermaid
graph TD
    A[连接失败] --> B{有备用代理?}
    B -->|是| C[尝试备用代理]
    B -->|否| D[返回错误]
    C --> E{连接成功?}
    E -->|是| F[使用备用代理]
    E -->|否| B
```

### 3. 日志记录

系统提供分级日志记录：
- **DEBUG**: 详细的调试信息
- **INFO**: 一般信息
- **WARN**: 警告信息
- **ERROR**: 错误信息

## 性能优化策略

### 1. 连接复用

- **HTTP/2**: 支持HTTP/2多路复用
- **连接池**: 复用TCP连接减少握手开销
- **Keep-Alive**: 保持长连接

### 2. 缓存策略

- **DNS缓存**: 缓存DNS查询结果
- **规则缓存**: 缓存规则匹配结果
- **连接缓存**: 缓存代理连接

### 3. 零拷贝优化

在可能的情况下使用零拷贝技术：
- **splice系统调用**: Linux下的零拷贝
- **sendfile**: 文件传输优化
- **内存映射**: 大文件处理优化

这种精心设计的数据流处理机制确保了Mihomo能够高效、稳定地处理大量并发连接，同时提供了灵活的路由和代理功能。

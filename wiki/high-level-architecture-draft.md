# 高层架构

## 目录
- [概述](#概述)
- [整体设计](#整体设计)
  - [模块化架构](#模块化架构)
  - [事件驱动架构](#事件驱动架构)
  - [并发处理模型](#并发处理模型)
  - [组件初始化流程](#组件初始化流程)
  - [模块间通信机制](#模块间通信机制)
- [核心层级](#核心层级)
- [设计模式](#设计模式)
- [架构图](#架构图)
- [数据流](#数据流)
- [事件循环机制](#事件循环机制)
- [资源管理策略](#资源管理策略)

## 概述

Mihomo是一个基于Go语言开发的高性能网络代理内核，支持多种协议和代理方式。项目采用模块化、事件驱动的架构设计，通过YAML配置文件进行管理，提供了完整的网络流量处理解决方案。

核心特点包括：
- **高性能并发处理**: 基于goroutine的轻量级并发模型
- **模块化设计**: 松耦合的组件架构，支持插件式扩展
- **事件驱动**: 基于观察者模式的异步事件处理
- **热重载支持**: 配置变更无需重启服务
- **多协议支持**: 统一的适配器接口支持各种代理协议

## 整体设计

### 模块化架构

Mihomo采用高度模块化的设计，主要模块包括：

- **入站监听器（Inbound Listeners）**: 处理各种协议的入站连接
  - 位置：`listener/` 目录
  - 支持协议：HTTP、SOCKS4/5、VMess、VLESS、Trojan、TUN等
  - 核心接口：`C.InboundListener`

- **规则引擎（Rule Engine）**: 根据配置规则进行流量路由
  - 位置：`rules/` 目录
  - 规则类型：域名、IP、端口、进程、逻辑规则等
  - 核心接口：`C.Rule`

- **出站代理（Outbound Proxies）**: 管理各种代理协议的出站连接
  - 位置：`adapter/outbound/` 目录
  - 支持协议：Shadowsocks、VMess、VLESS、Trojan、Hysteria等
  - 核心接口：`C.ProxyAdapter`

- **DNS解析器（DNS Resolver）**: 提供DNS解析和增强功能
  - 位置：`dns/` 目录
  - 功能：上游DNS管理、缓存、Fake IP、分流策略
  - 核心接口：`resolver.Resolver`

- **配置管理（Configuration）**: 统一的配置解析和管理
  - 位置：`config/` 目录
  - 功能：YAML解析、配置验证、热重载
  - 核心结构：`config.Config`

- **API控制器（API Controller）**: RESTful API接口用于控制和监控
  - 位置：`hub/route/` 目录
  - 功能：配置管理、状态查询、连接统计
  - 核心路由：`hub/route/server.go`

### 事件驱动架构

系统采用事件驱动模式，通过观察者模式实现组件间的解耦。核心实现位于`common/observable/`包：

#### 观察者模式实现

<augment_code_snippet path="common/observable/observable.go" mode="EXCERPT">
````go
type Observable[T any] struct {
    iterable Iterable[T]
    listener map[Subscription[T]]*Subscriber[T]
    mux      sync.Mutex
    done     bool
    stopCh   chan struct{}
}

func (o *Observable[T]) Subscribe() (Subscription[T], error) {
    o.mux.Lock()
    defer o.mux.Unlock()
    if o.done {
        return nil, errors.New("observable is closed")
    }
    subscriber := newSubscriber[T]()
    o.listener[subscriber.Out()] = subscriber
    return subscriber.Out(), nil
}
````
</augment_code_snippet>

#### 事件类型和处理

- **配置变更事件**: 通过`hub.Parse()`触发，调用`executor.ApplyConfig()`
- **连接状态变化**: 通过`tunnel.HandleTCPConn()`和`tunnel.HandleUDPPacket()`处理
- **规则更新事件**: 通过`ruleUpdateCallback`回调机制同步到相关组件
- **代理状态变化**: 通过`adapter.Proxy.URLTest()`更新延迟和可用性状态

### 并发处理模型

利用Go语言的goroutine特性实现高并发处理：

#### TCP连接并发模型

每个TCP连接使用独立的goroutine处理，实现在`tunnel/tunnel.go`：

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func (t tunnel) HandleTCPConn(conn net.Conn, metadata *C.Metadata) {
    connCtx := icontext.NewConnContext(conn, metadata)
    handleTCPConn(connCtx)
}

func handleTCPConn(connCtx C.ConnContext) {
    defer func(conn net.Conn) {
        _ = conn.Close()
    }(connCtx.Conn())
    // 处理连接逻辑...
}
````
</augment_code_snippet>

#### UDP工作池模型

UDP流量通过工作池模式分发处理，避免goroutine过度创建：

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
func initUDP() {
    numUDPWorkers := 4
    if num := runtime.GOMAXPROCS(0); num > numUDPWorkers {
        numUDPWorkers = num
    }

    udpQueues = make([]chan C.PacketAdapter, numUDPWorkers)
    for i := 0; i < numUDPWorkers; i++ {
        queue := make(chan C.PacketAdapter, queueCapacity)
        udpQueues[i] = queue
        go processUDP(queue)
    }
}
````
</augment_code_snippet>

#### 同步原语使用

- **sync.Mutex**: 保护共享状态，如`observable.Observable`中的listener map
- **sync.RWMutex**: 读写分离锁，用于频繁读取的数据结构
- **sync.Pool**: 对象池，用于缓冲区复用，如`common/pool/buffer.go`
- **sync.Once**: 确保初始化只执行一次，如UDP工作池初始化
- **atomic**: 原子操作，用于计数器和状态标志

### 组件初始化流程

系统启动时的组件初始化顺序在`hub/executor/executor.go`中定义：

```mermaid
graph TD
    A[main.go启动] --> B[hub.Parse配置解析]
    B --> C[executor.ApplyConfig应用配置]
    C --> D[updateExperimental实验性功能]
    D --> E[updateUsers用户认证]
    E --> F[updateProxies代理配置]
    F --> G[updateRules规则配置]
    G --> H[updateSniffer流量嗅探]
    H --> I[updateHosts主机映射]
    I --> J[updateGeneral通用配置]
    J --> K[updateNTP时间同步]
    K --> L[updateDNS DNS配置]
    L --> M[updateListeners监听器]
    M --> N[updateTun TUN接口]
    N --> O[updateIPTables防火墙规则]
    O --> P[updateTunnels隧道配置]
    P --> Q[tunnel.OnRunning运行状态]
```

#### 详细初始化步骤

1. **配置解析阶段** (`config.Parse`)
   - YAML文件解析和验证
   - 默认值填充和类型转换
   - 配置项依赖关系检查

2. **组件更新阶段** (`executor.ApplyConfig`)
   - 按依赖顺序更新各组件
   - 使用goroutine并发加载Provider
   - 错误处理和回滚机制

3. **服务启动阶段** (`tunnel.OnRunning`)
   - 监听器启动和端口绑定
   - 后台服务启动（如NTP同步）
   - 状态监控和健康检查

### 模块间通信机制

#### 接口抽象

通过接口定义实现模块间的松耦合：

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
type ProxyAdapter interface {
    Name() string
    Type() AdapterType
    Addr() string
    SupportUDP() bool
    DialContext(ctx context.Context, metadata *Metadata) (Conn, error)
    ListenPacketContext(ctx context.Context, metadata *Metadata) (PacketConn, error)
}
````
</augment_code_snippet>

#### 依赖注入

通过构造函数和配置参数实现依赖注入：

- **Tunnel接口**: 作为核心枢纽，注入到各个监听器
- **Resolver接口**: DNS解析器注入到代理适配器
- **Provider接口**: 代理和规则提供者的统一接口

#### 事件总线

通过回调机制实现事件通知：

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
var ruleUpdateCallback = utils.NewCallback[provider.RuleProvider]()

func (t tunnel) RuleUpdateCallback() *utils.Callback[provider.RuleProvider] {
    return ruleUpdateCallback
}
````
</augment_code_snippet>

## 核心层级

### 1. 入站层（Inbound Layer）

负责接收和处理各种协议的入站连接，实现统一的连接接入接口。

#### 监听器架构

```mermaid
graph TB
    subgraph "入站监听器层"
        A[HTTP监听器<br/>listener/http] --> M[混合监听器<br/>listener/mixed]
        B[SOCKS监听器<br/>listener/socks] --> M
        C[VMess监听器<br/>listener/sing_vmess] --> N[协议特定监听器]
        D[VLESS监听器] --> N
        E[Trojan监听器] --> N
        F[TUN接口<br/>listener/sing_tun] --> O[系统级监听器]
        G[透明代理<br/>listener/tproxy] --> O
        H[重定向<br/>listener/redir] --> O
    end

    subgraph "连接处理层"
        M --> P[连接处理器<br/>adapter/inbound]
        N --> P
        O --> P
        P --> Q[元数据提取器]
        Q --> R[隧道接口<br/>tunnel.Tunnel]
    end
```

#### 入站监听器类型详解

**HTTP/HTTPS代理监听器** (`listener/http/`)
- 功能：处理HTTP CONNECT和HTTPS代理请求
- 实现：`http.NewListener()`
- 特性：支持认证、Keep-Alive、代理链
- 接口：实现`C.InboundListener`接口

**SOCKS4/SOCKS5代理监听器** (`listener/socks/`)
- 功能：处理SOCKS协议连接
- 实现：`socks.NewListener()`
- 特性：支持UDP关联、认证、IPv6
- 协议支持：SOCKS4、SOCKS4A、SOCKS5

**混合端口监听器** (`listener/mixed/`)
- 功能：自动检测HTTP和SOCKS协议
- 实现：`mixed.NewListener()`
- 特性：协议自动识别、单端口多协议
- 检测逻辑：基于首字节判断协议类型

**透明代理监听器** (`listener/tproxy/`, `listener/redir/`)
- 功能：处理iptables重定向的透明代理流量
- 实现：`tproxy.NewListener()`, `redir.NewListener()`
- 特性：原始目标地址保留、内核级流量拦截
- 平台支持：Linux (TPROXY), macOS/BSD (pfctl)

**TUN接口监听器** (`listener/sing_tun/`)
- 功能：虚拟网络接口，处理三层网络流量
- 实现：基于sing-tun库
- 特性：全局代理、路由表管理、DNS劫持
- 平台支持：跨平台TUN/TAP接口

**协议特定监听器**
- VMess监听器：`listener/sing_vmess/`
- Shadowsocks监听器：`listener/shadowsocks/`
- TUIC监听器：`listener/tuic/`

#### 连接处理流程

<augment_code_snippet path="listener/listener.go" mode="EXCERPT">
````go
func ReCreateHTTP(port int, tunnel C.Tunnel) {
    httpMux.Lock()
    defer httpMux.Unlock()

    var err error
    defer func() {
        if err != nil {
            log.Errorln("Start HTTP server error: %s", err.Error())
        }
    }()

    addr := genAddr(bindAddress, port, allowLan)
    if httpListener != nil {
        if httpListener.RawAddress() == addr {
            return
        }
        httpListener.Close()
        httpListener = nil
    }

    if portIsZero(port) {
        return
    }

    httpListener, err = http.NewListener(addr, tunnel)
    if err != nil {
        log.Errorln("Start HTTP server error: %s", err.Error())
        return
    }

    log.Infoln("HTTP proxy listening at: %s", httpListener.Address())
}
````
</augment_code_snippet>

### 2. 规则引擎层（Rule Engine Layer）

根据配置的规则对流量进行分类和路由，支持复杂的逻辑运算和动态规则更新。

#### 规则类型架构

```mermaid
graph TB
    subgraph "基础规则类型"
        A[域名规则<br/>DOMAIN/DOMAIN-SUFFIX/DOMAIN-KEYWORD] --> G[规则匹配器<br/>rules/]
        B[IP规则<br/>IP-CIDR/GEOIP/IP-ASN] --> G
        C[端口规则<br/>SRC-PORT/DST-PORT] --> G
        D[进程规则<br/>PROCESS-NAME/PROCESS-PATH] --> G
    end

    subgraph "逻辑规则类型"
        E[逻辑运算<br/>AND/OR/NOT] --> H[逻辑规则处理器<br/>rules/logic.go]
        F[子规则<br/>SUB-RULE] --> H
    end

    subgraph "动态规则类型"
        I[规则集<br/>RULE-SET] --> J[规则提供者<br/>rules/provider/]
        K[GeoIP数据库<br/>component/geodata/] --> J
        L[GeoSite数据库] --> J
    end

    G --> M[规则引擎<br/>tunnel/tunnel.go]
    H --> M
    J --> M
```

#### 规则接口定义

<augment_code_snippet path="constant/rule.go" mode="EXCERPT">
````go
type Rule interface {
    RuleType() RuleType
    Match(metadata *Metadata, helper RuleMatchHelper) (bool, string)
    Adapter() string
    Payload() string
    ProviderNames() []string
}

type RuleMatchHelper struct {
    ResolveIP   func()
    FindProcess func()
}
````
</augment_code_snippet>

#### 规则匹配优化

- **缓存机制**: 域名和IP解析结果缓存
- **并行匹配**: 多规则并行评估
- **短路求值**: 逻辑运算的短路优化
- **预编译**: 正则表达式预编译

### 3. 出站层（Outbound Layer）

管理各种代理协议的出站连接，提供统一的代理接口和负载均衡策略。

#### 代理适配器架构

```mermaid
graph TB
    subgraph "直接连接"
        A[DIRECT<br/>adapter/outbound/direct.go] --> M[基础适配器<br/>adapter/outbound/base.go]
        B[REJECT<br/>adapter/outbound/reject.go] --> M
    end

    subgraph "代理协议"
        C[Shadowsocks<br/>adapter/outbound/shadowsocks.go] --> M
        D[VMess<br/>adapter/outbound/vmess.go] --> M
        E[VLESS<br/>adapter/outbound/vless.go] --> M
        F[Trojan<br/>adapter/outbound/trojan.go] --> M
        G[Hysteria<br/>adapter/outbound/hysteria.go] --> M
        H[WireGuard<br/>adapter/outbound/wireguard.go] --> M
    end

    subgraph "代理组"
        I[Selector<br/>adapter/outboundgroup/selector.go] --> N[代理组基类<br/>adapter/outboundgroup/groupbase.go]
        J[URLTest<br/>adapter/outboundgroup/urltest.go] --> N
        K[Fallback<br/>adapter/outboundgroup/fallback.go] --> N
        L[LoadBalance<br/>adapter/outboundgroup/loadbalance.go] --> N
    end

    M --> O[代理适配器接口<br/>C.ProxyAdapter]
    N --> O
```

#### 基础适配器实现

<augment_code_snippet path="adapter/outbound/base.go" mode="EXCERPT">
````go
type Base struct {
    name   string
    addr   string
    iface  string
    tp     C.AdapterType
    udp    bool
    xudp   bool
    tfo    bool
    mpTcp  bool
    rmark  int
    id     string
    prefer C.DNSPrefer
}

func (b *Base) Name() string {
    return b.name
}

func (b *Base) Type() C.AdapterType {
    return b.tp
}
````
</augment_code_snippet>

#### 代理组策略

**选择器组 (Selector)**
- 功能：手动选择代理节点
- 实现：`adapter/outboundgroup/selector.go`
- 特性：支持API动态切换、配置持久化

**URL测试组 (URLTest)**
- 功能：自动选择延迟最低的节点
- 实现：`adapter/outboundgroup/urltest.go`
- 特性：定期健康检查、自动故障转移

**故障转移组 (Fallback)**
- 功能：按顺序尝试节点，直到成功
- 实现：`adapter/outboundgroup/fallback.go`
- 特性：健康检查、快速故障检测

**负载均衡组 (LoadBalance)**
- 功能：在多个节点间分发流量
- 实现：`adapter/outboundgroup/loadbalance.go`
- 策略：轮询、随机、一致性哈希

### 4. DNS层（DNS Layer）

提供DNS解析和增强功能，支持多种解析策略和缓存机制。

#### DNS架构组件

```mermaid
graph TB
    subgraph "DNS解析器"
        A[主解析器<br/>dns/resolver.go] --> F[DNS增强器<br/>dns/enhancer.go]
        B[代理解析器<br/>ProxyResolver] --> F
        C[直连解析器<br/>DirectResolver] --> F
    end

    subgraph "DNS服务器"
        D[本地DNS服务器<br/>dns/server.go] --> G[DNS处理器]
        E[上游DNS管理<br/>dns/upstream.go] --> G
    end

    subgraph "DNS功能模块"
        H[Fake IP池<br/>component/fakeip/] --> I[DNS功能集成]
        J[DNS缓存<br/>component/cache/] --> I
        K[域名映射<br/>component/trie/] --> I
        L[DNS分流<br/>dns/policy.go] --> I
    end

    F --> M[解析器接口<br/>resolver.Resolver]
    G --> M
    I --> M
```

#### DNS功能特性

**上游DNS服务器管理**
- 支持协议：UDP、TCP、DoH、DoT、DoQ
- 负载均衡：轮询、随机、最快响应
- 故障转移：自动切换故障上游

**DNS缓存机制**
- 多级缓存：内存缓存、持久化缓存
- TTL管理：动态TTL调整、缓存预刷新
- 缓存策略：LRU淘汰、容量限制

**Fake IP模式**
- 功能：为域名分配虚拟IP地址
- 优势：避免DNS泄露、加速连接建立
- 实现：`component/fakeip/pool.go`

**DNS分流策略**
- 域名分流：根据域名选择不同上游
- 地理分流：根据IP地理位置分流
- 自定义规则：支持复杂分流逻辑

## 设计模式

### 1. 适配器模式（Adapter Pattern）

**位置**: `adapter/`包和`constant/adapters.go`
**作用**: 为不同的代理协议提供统一的接口，实现协议无关的上层逻辑

#### 接口定义

<augment_code_snippet path="constant/adapters.go" mode="EXCERPT">
````go
type ProxyAdapter interface {
    Name() string
    Type() AdapterType
    Addr() string
    SupportUDP() bool
    DialContext(ctx context.Context, metadata *Metadata) (Conn, error)
    ListenPacketContext(ctx context.Context, metadata *Metadata) (PacketConn, error)
    StreamConnContext(ctx context.Context, c net.Conn, metadata *Metadata) (net.Conn, error)
    SupportUOT() bool
    SupportWithDialer() NetWork
    IsL3Protocol(metadata *Metadata) bool
    Unwrap(metadata *Metadata, touch bool) Proxy
}
````
</augment_code_snippet>

#### 实现示例

每个代理协议都实现相同的接口，但内部实现完全不同：

- **Shadowsocks适配器**: `adapter/outbound/shadowsocks.go`
- **VMess适配器**: `adapter/outbound/vmess.go`
- **Trojan适配器**: `adapter/outbound/trojan.go`

#### 优势

- **协议扩展性**: 新增协议只需实现接口
- **代码复用**: 上层逻辑无需关心具体协议
- **测试友好**: 可以轻松mock不同协议

### 2. 观察者模式（Observer Pattern）

**位置**: `common/observable/`包
**作用**: 实现配置变更通知和事件分发，支持异步事件处理

#### 核心实现

<augment_code_snippet path="common/observable/observable.go" mode="EXCERPT">
````go
type Observable[T any] struct {
    iterable Iterable[T]
    listener map[Subscription[T]]*Subscriber[T]
    mux      sync.Mutex
    done     bool
    stopCh   chan struct{}
}

func (o *Observable[T]) process() {
    for item := range o.iterable {
        o.mux.Lock()
        for _, sub := range o.listener {
            sub.Emit(item)
        }
        o.mux.Unlock()
    }
    o.close()
}
````
</augment_code_snippet>

#### 订阅者实现

<augment_code_snippet path="common/observable/subscriber.go" mode="EXCERPT">
````go
type Subscriber[T any] struct {
    buffer chan T
    once   sync.Once
}

func (s *Subscriber[T]) Emit(item T) {
    s.buffer <- item
}

func newSubscriber[T any]() *Subscriber[T] {
    return &Subscriber[T]{
        buffer: make(chan T, 200),
    }
}
````
</augment_code_snippet>

#### 应用场景

- **日志事件**: `log`包中的日志事件分发
- **连接状态**: 连接建立和断开事件
- **配置变更**: 配置文件变更通知
- **统计数据**: 流量统计和性能指标

### 3. 工厂模式（Factory Pattern）

**位置**: `adapter/outbound/`、`rules/`、`dns/`等包
**作用**: 根据配置动态创建代理、规则和DNS解析器实例

#### 代理工厂实现

代理创建通过注册机制实现：

```go
// 代理构造函数类型
type ProxyConstructor func(option map[string]any) (C.Proxy, error)

// 全局注册表
var constructors = map[string]ProxyConstructor{
    "ss":       NewShadowSocks,
    "vmess":    NewVmess,
    "trojan":   NewTrojan,
    "hysteria": NewHysteria,
}

// 工厂方法
func NewProxy(tp string, option map[string]any) (C.Proxy, error) {
    constructor, exist := constructors[tp]
    if !exist {
        return nil, fmt.Errorf("unsupported proxy type: %s", tp)
    }
    return constructor(option)
}
```

#### 规则工厂实现

规则创建同样使用注册机制：

```go
type RuleConstructor func(params []string, target string) (C.Rule, error)

var ruleConstructors = map[string]RuleConstructor{
    "DOMAIN":        NewDomain,
    "DOMAIN-SUFFIX": NewDomainSuffix,
    "GEOIP":         NewGEOIP,
    "IP-CIDR":       NewIPCIDR,
}
```

#### 优势

- **插件化扩展**: 支持运行时注册新类型
- **配置驱动**: 完全由配置文件驱动实例创建
- **类型安全**: 编译时检查构造函数签名

### 4. 策略模式（Strategy Pattern）

**位置**: `adapter/outboundgroup/`包
**作用**: 代理组的不同选择策略，支持多种负载均衡算法

#### 策略接口

```go
type ProxyGroup interface {
    C.ProxyAdapter
    Now() string
    Set(name string) error
    GetProxies(touch bool) []C.Proxy
    Touch()
}
```

#### 具体策略实现

**URL测试策略** (`adapter/outboundgroup/urltest.go`)
- 算法：选择延迟最低的代理
- 特性：定期健康检查、自动切换
- 适用场景：自动优选、性能优先

**故障转移策略** (`adapter/outboundgroup/fallback.go`)
- 算法：按优先级顺序尝试代理
- 特性：快速故障检测、顺序回退
- 适用场景：高可用、稳定性优先

**负载均衡策略** (`adapter/outboundgroup/loadbalance.go`)
- 算法：轮询、随机、一致性哈希
- 特性：流量分散、会话保持
- 适用场景：高并发、流量分发

**选择器策略** (`adapter/outboundgroup/selector.go`)
- 算法：手动选择代理
- 特性：用户控制、配置持久化
- 适用场景：手动控制、测试调试

#### 策略切换机制

```go
type GroupBase struct {
    *outbound.Base
    providers []provider.ProxyProvider
    proxies   []C.Proxy
    selected  string
}

func (gb *GroupBase) GetProxies(touch bool) []C.Proxy {
    if touch {
        gb.Touch()
    }
    return gb.proxies
}
```

### 5. 单例模式（Singleton Pattern）

**位置**: `tunnel/`包、`resolver/`包等
**作用**: 全局实例管理，确保系统中只有一个核心组件实例

#### 隧道单例实现

<augment_code_snippet path="tunnel/tunnel.go" mode="EXCERPT">
````go
type tunnel struct{}

var Tunnel = tunnel{}
var _ C.Tunnel = Tunnel
var _ provider.Tunnel = Tunnel

func (t tunnel) HandleTCPConn(conn net.Conn, metadata *C.Metadata) {
    connCtx := icontext.NewConnContext(conn, metadata)
    handleTCPConn(connCtx)
}
````
</augment_code_snippet>

#### 解析器单例实现

```go
var (
    DefaultResolver     Resolver
    DefaultHostMapper   ResolverEnhancer
    DefaultLocalServer  *LocalServer
    DefaultHosts        Hosts
)
```

#### 单例的优势和注意事项

**优势**:
- **全局访问**: 系统任何地方都可以访问核心组件
- **状态一致**: 避免多实例导致的状态不一致
- **资源节约**: 避免重复创建昂贵的资源

**注意事项**:
- **测试困难**: 单例状态可能影响测试隔离
- **并发安全**: 需要考虑多goroutine访问安全
- **初始化顺序**: 需要确保正确的初始化顺序

### 6. 建造者模式（Builder Pattern）

**位置**: `config/`包
**作用**: 复杂配置对象的构建，支持链式调用和参数验证

#### 配置建造者

```go
type ConfigBuilder struct {
    config *Config
    errors []error
}

func NewConfigBuilder() *ConfigBuilder {
    return &ConfigBuilder{
        config: &Config{},
        errors: make([]error, 0),
    }
}

func (cb *ConfigBuilder) WithGeneral(general *General) *ConfigBuilder {
    cb.config.General = general
    return cb
}

func (cb *ConfigBuilder) WithDNS(dns *DNS) *ConfigBuilder {
    if dns != nil {
        cb.config.DNS = dns
    } else {
        cb.errors = append(cb.errors, errors.New("DNS config cannot be nil"))
    }
    return cb
}

func (cb *ConfigBuilder) Build() (*Config, error) {
    if len(cb.errors) > 0 {
        return nil, fmt.Errorf("config build failed: %v", cb.errors)
    }
    return cb.config, nil
}
```

### 7. 装饰器模式（Decorator Pattern）

**位置**: `common/net/`包、`adapter/`包
**作用**: 为连接对象添加额外功能，如缓冲、统计、加密等

#### 连接装饰器

<augment_code_snippet path="context/conn.go" mode="EXCERPT">
````go
type ConnContext struct {
    id       uuid.UUID
    metadata *C.Metadata
    conn     *N.BufferedConn
}

func NewConnContext(conn net.Conn, metadata *C.Metadata) *ConnContext {
    return &ConnContext{
        id:       utils.NewUUIDV4(),
        metadata: metadata,
        conn:     N.NewBufferedConn(conn),
    }
}
````
</augment_code_snippet>

#### 装饰器链

- **BufferedConn**: 添加缓冲功能
- **StatConn**: 添加统计功能
- **TimeoutConn**: 添加超时控制
- **TLSConn**: 添加TLS加密

## 架构图

### 整体架构图

```mermaid
graph TB
    subgraph "客户端层"
        Client[客户端应用]
        Browser[浏览器]
        App[移动应用]
    end

    subgraph "入站监听层"
        HTTP[HTTP监听器<br/>:8080] --> Tunnel[隧道核心<br/>tunnel.Tunnel]
        SOCKS[SOCKS监听器<br/>:1080] --> Tunnel
        Mixed[混合监听器<br/>:7890] --> Tunnel
        TUN[TUN接口<br/>tun0] --> Tunnel
        TPROXY[透明代理<br/>iptables] --> Tunnel
        VMess[VMess监听器<br/>:8443] --> Tunnel
    end

    subgraph "核心处理层"
        Tunnel --> MetaExtract[元数据提取器<br/>metadata.go]
        MetaExtract --> RuleEngine[规则引擎<br/>rules/]
        RuleEngine --> DNSResolver[DNS解析器<br/>dns/]
        DNSResolver --> ProxySelector[代理选择器<br/>adapter/]
    end

    subgraph "出站代理层"
        ProxySelector --> Direct[直连<br/>DIRECT]
        ProxySelector --> SS[Shadowsocks<br/>SS节点]
        ProxySelector --> VMess2[VMess<br/>V2Ray节点]
        ProxySelector --> Trojan[Trojan<br/>Trojan节点]
        ProxySelector --> Group[代理组<br/>负载均衡]
    end

    subgraph "管理控制层"
        Config[配置管理<br/>config/] --> RuleEngine
        Config --> DNSResolver
        Config --> ProxySelector
        API[RESTful API<br/>hub/route/] --> Config
        WebUI[Web控制面板] --> API
        Stats[统计监控<br/>statistic/] --> API
    end

    subgraph "外部服务"
        Target[目标服务器]
        DNS[上游DNS服务器]
        GeoData[GeoIP/GeoSite数据库]
    end

    Client --> HTTP
    Browser --> Mixed
    App --> SOCKS

    Direct --> Target
    SS --> Target
    VMess2 --> Target
    Trojan --> Target

    DNSResolver --> DNS
    RuleEngine --> GeoData

    Tunnel --> Stats
    ProxySelector --> Stats

    classDef inbound fill:#e1f5fe
    classDef core fill:#f3e5f5
    classDef outbound fill:#e8f5e8
    classDef management fill:#fff3e0
    classDef external fill:#fce4ec

    class HTTP,SOCKS,Mixed,TUN,TPROXY,VMess inbound
    class Tunnel,MetaExtract,RuleEngine,DNSResolver,ProxySelector core
    class Direct,SS,VMess2,Trojan,Group outbound
    class Config,API,WebUI,Stats management
    class Target,DNS,GeoData external
```

### 组件交互图

```mermaid
graph LR
    subgraph "配置系统"
        ConfigFile[config.yaml] --> ConfigParser[配置解析器]
        ConfigParser --> ConfigManager[配置管理器]
        ConfigManager --> HotReload[热重载机制]
    end

    subgraph "监听系统"
        ListenerManager[监听器管理器] --> HTTPListener[HTTP监听器]
        ListenerManager --> SOCKSListener[SOCKS监听器]
        ListenerManager --> TUNListener[TUN监听器]
    end

    subgraph "规则系统"
        RuleManager[规则管理器] --> DomainRule[域名规则]
        RuleManager --> IPRule[IP规则]
        RuleManager --> ProcessRule[进程规则]
        RuleManager --> LogicRule[逻辑规则]
    end

    subgraph "代理系统"
        ProxyManager[代理管理器] --> ProxyAdapter[代理适配器]
        ProxyManager --> ProxyGroup[代理组]
        ProxyAdapter --> HealthCheck[健康检查]
    end

    subgraph "DNS系统"
        DNSManager[DNS管理器] --> Resolver[解析器]
        DNSManager --> Cache[DNS缓存]
        DNSManager --> FakeIP[Fake IP池]
    end

    ConfigManager --> ListenerManager
    ConfigManager --> RuleManager
    ConfigManager --> ProxyManager
    ConfigManager --> DNSManager

    HotReload --> ListenerManager
    HotReload --> RuleManager
    HotReload --> ProxyManager
    HotReload --> DNSManager
```

### 数据流架构图

```mermaid
flowchart TD
    subgraph "入站数据流"
        A[客户端连接] --> B{协议检测}
        B -->|HTTP| C[HTTP处理器]
        B -->|SOCKS| D[SOCKS处理器]
        B -->|TUN| E[TUN处理器]
        C --> F[元数据提取]
        D --> F
        E --> F
    end

    subgraph "规则处理流"
        F --> G[规则匹配]
        G --> H{匹配结果}
        H -->|DIRECT| I[直连处理]
        H -->|PROXY| J[代理处理]
        H -->|REJECT| K[拒绝处理]
    end

    subgraph "DNS解析流"
        J --> L{需要DNS解析?}
        L -->|是| M[DNS查询]
        L -->|否| N[使用现有IP]
        M --> O{Fake IP模式?}
        O -->|是| P[分配Fake IP]
        O -->|否| Q[真实DNS解析]
        P --> N
        Q --> N
    end

    subgraph "出站数据流"
        I --> R[建立直连]
        N --> S[选择代理]
        S --> T[建立代理连接]
        R --> U[数据转发]
        T --> U
        K --> V[连接拒绝]
    end

    subgraph "统计监控流"
        U --> W[流量统计]
        V --> W
        W --> X[API接口]
        X --> Y[Web控制台]
    end
```

## 数据流

### TCP连接处理流程

```mermaid
sequenceDiagram
    participant Client as 客户端
    participant Listener as 监听器
    participant Tunnel as 隧道核心
    participant Rules as 规则引擎
    participant DNS as DNS解析器
    participant Proxy as 代理适配器
    participant Target as 目标服务器

    Client->>Listener: 建立连接
    Listener->>Tunnel: HandleTCPConn()
    Tunnel->>Tunnel: 提取元数据
    Tunnel->>Rules: 规则匹配
    Rules-->>Tunnel: 匹配结果(PROXY)
    Tunnel->>DNS: 解析域名
    DNS-->>Tunnel: IP地址
    Tunnel->>Proxy: DialContext()
    Proxy->>Target: 建立连接
    Target-->>Proxy: 连接确认
    Proxy-->>Tunnel: 连接就绪

    loop 数据转发
        Client->>Tunnel: 发送数据
        Tunnel->>Proxy: 转发数据
        Proxy->>Target: 发送到目标
        Target->>Proxy: 响应数据
        Proxy->>Tunnel: 转发响应
        Tunnel->>Client: 返回数据
    end

    Client->>Tunnel: 关闭连接
    Tunnel->>Proxy: 关闭代理连接
    Proxy->>Target: 关闭目标连接
```

#### 详细处理步骤

1. **连接接收阶段**
   - 监听器接收TCP连接：`listener.Accept()`
   - 创建连接上下文：`icontext.NewConnContext()`
   - 启动处理goroutine：`go handleTCPConn()`

2. **元数据提取阶段**
   - 协议解析：HTTP CONNECT、SOCKS握手
   - 目标地址提取：域名或IP地址
   - 源地址记录：客户端IP和端口
   - 进程信息获取：通过系统调用获取进程名

3. **规则匹配阶段**
   - 规则遍历：按配置顺序匹配规则
   - 条件判断：域名、IP、端口、进程等
   - 结果确定：DIRECT、PROXY、REJECT
   - 代理选择：具体代理节点或代理组

4. **DNS解析阶段**
   - 解析需求判断：是否为域名
   - 缓存查询：检查DNS缓存
   - 上游查询：向DNS服务器查询
   - Fake IP处理：分配虚拟IP地址

5. **连接建立阶段**
   - 代理连接：通过代理建立到目标的连接
   - 握手处理：协议特定的握手过程
   - 连接验证：确认连接可用性
   - 错误处理：连接失败时的重试或回退

6. **数据转发阶段**
   - 双向转发：客户端↔代理↔目标服务器
   - 缓冲管理：使用缓冲池优化性能
   - 流量统计：记录上传和下载流量
   - 连接保持：处理Keep-Alive和超时

### UDP包处理流程

```mermaid
flowchart TD
    A[UDP包到达] --> B[包适配器创建]
    B --> C[计算哈希值]
    C --> D[选择工作队列]
    D --> E{队列是否满?}
    E -->|是| F[丢弃包]
    E -->|否| G[加入队列]
    G --> H[工作协程处理]
    H --> I[元数据验证]
    I --> J{元数据有效?}
    J -->|否| K[丢弃包]
    J -->|是| L[规则匹配]
    L --> M[NAT表查询]
    M --> N{会话存在?}
    N -->|是| O[使用现有会话]
    N -->|否| P[创建新会话]
    P --> Q[启动发送协程]
    O --> R[发送包]
    Q --> R
    R --> S[目标服务器]
```

#### UDP处理特点

1. **工作池模式**
   - 工作协程数量：`max(4, GOMAXPROCS)`
   - 队列容量：`queueCapacity = 200`
   - 负载均衡：基于包键值的哈希分发

2. **NAT表管理**
   - 会话跟踪：维护客户端到目标的映射
   - 超时清理：自动清理过期会话
   - 并发安全：使用读写锁保护

3. **包发送器**
   - 异步发送：非阻塞的包发送机制
   - 缓冲管理：包缓冲和批量发送
   - 错误处理：发送失败时的重试逻辑

### 配置热重载流程

```mermaid
sequenceDiagram
    participant File as 配置文件
    participant Signal as 信号处理
    participant Hub as Hub控制器
    participant Executor as 执行器
    participant Components as 各组件

    File->>Signal: 文件变更/SIGHUP信号
    Signal->>Hub: hub.Parse()
    Hub->>Hub: 配置解析和验证
    Hub->>Executor: executor.ApplyConfig()

    Executor->>Components: updateProxies()
    Executor->>Components: updateRules()
    Executor->>Components: updateDNS()
    Executor->>Components: updateListeners()

    Components-->>Executor: 更新完成
    Executor->>Executor: tunnel.OnRunning()
    Executor-->>Hub: 配置应用完成
    Hub-->>Signal: 重载成功
```

#### 热重载机制

1. **变更检测**
   - 信号监听：监听SIGHUP信号
   - 文件监控：可选的文件系统监控
   - API触发：通过RESTful API触发

2. **配置解析**
   - 语法验证：YAML格式和结构验证
   - 语义检查：配置项的逻辑一致性
   - 增量对比：与当前配置的差异分析

3. **组件更新**
   - 依赖排序：按组件依赖关系排序更新
   - 原子操作：确保更新过程的原子性
   - 回滚机制：更新失败时的回滚策略

4. **连接迁移**
   - 平滑过渡：现有连接继续使用旧配置
   - 新连接：使用新配置处理新连接
   - 资源清理：清理不再使用的资源

## 事件循环机制

### 主事件循环

Mihomo的主事件循环在`main.go`中实现：

<augment_code_snippet path="main.go" mode="EXCERPT">
````go
termSign := make(chan os.Signal, 1)
hupSign := make(chan os.Signal, 1)
signal.Notify(termSign, syscall.SIGINT, syscall.SIGTERM)
signal.Notify(hupSign, syscall.SIGHUP)

for {
    select {
    case <-termSign:
        return
    case <-hupSign:
        if err := hub.Parse(configBytes, options...); err != nil {
            log.Errorln("Parse config error: %s", err.Error())
        }
    }
}
````
</augment_code_snippet>

### 组件事件循环

#### 隧道事件处理

```go
// TCP连接处理循环
func handleTCPConn(connCtx C.ConnContext) {
    defer func(conn net.Conn) {
        _ = conn.Close()
    }(connCtx.Conn())

    // 事件处理逻辑
    metadata := connCtx.Metadata()
    proxy, rule, err := resolveMetadata(metadata)
    if err != nil {
        return
    }

    // 建立连接和数据转发
    remoteConn, err := proxy.DialContext(ctx, metadata)
    if err != nil {
        return
    }

    relay(connCtx.Conn(), remoteConn)
}
```

#### UDP包处理循环

```go
func processUDP(queue <-chan C.PacketAdapter) {
    for packet := range queue {
        handleUDPConn(packet)
    }
}
```

### 观察者事件循环

<augment_code_snippet path="common/observable/observable.go" mode="EXCERPT">
````go
func (o *Observable[T]) process() {
    for item := range o.iterable {
        o.mux.Lock()
        for _, sub := range o.listener {
            sub.Emit(item)
        }
        o.mux.Unlock()
    }
    o.close()
}
````
</augment_code_snippet>

## 资源管理策略

### 内存管理

#### 对象池化

<augment_code_snippet path="common/pool/buffer.go" mode="EXCERPT">
````go
var bufferPool = sync.Pool{New: func() any { return &bytes.Buffer{} }}

func GetBuffer() *bytes.Buffer {
    return bufferPool.Get().(*bytes.Buffer)
}

func PutBuffer(buf *bytes.Buffer) {
    buf.Reset()
    bufferPool.Put(buf)
}
````
</augment_code_snippet>

#### 内存分配器

<augment_code_snippet path="common/pool/alloc.go" mode="EXCERPT">
````go
type defaultAllocator struct {
    buffers [11]sync.Pool
}

func (alloc *defaultAllocator) Get(size int) []byte {
    switch {
    case size <= 0:
        return nil
    case size > 65536:
        return make([]byte, size)
    default:
        index := msb(size)
        if size != 1<<index {
            index += 1
        }
        return alloc.buffers[index-6].Get().([]byte)[:size]
    }
}
````
</augment_code_snippet>

### 连接管理

#### 连接池

```go
type ConnPool struct {
    pools map[string]*sync.Pool
    mutex sync.RWMutex
}

func (cp *ConnPool) Get(addr string) net.Conn {
    cp.mutex.RLock()
    pool, exists := cp.pools[addr]
    cp.mutex.RUnlock()

    if !exists {
        return nil
    }

    if conn := pool.Get(); conn != nil {
        return conn.(net.Conn)
    }
    return nil
}
```

#### 连接超时管理

```go
func setConnTimeout(conn net.Conn, timeout time.Duration) {
    if timeout > 0 {
        conn.SetDeadline(time.Now().Add(timeout))
    }
}
```

### 资源清理

#### 优雅关闭

<augment_code_snippet path="hub/executor/executor.go" mode="EXCERPT">
````go
func Shutdown() {
    listener.Cleanup()
    tproxy.CleanupTProxyIPTables()
    resolver.StoreFakePoolState()

    log.Warnln("Mihomo shutting down")
}
````
</augment_code_snippet>

#### 资源回收

```go
func cleanup() {
    // 关闭所有监听器
    for _, listener := range listeners {
        listener.Close()
    }

    // 清理DNS缓存
    resolver.DefaultResolver.Close()

    // 清理代理连接
    for _, proxy := range proxies {
        proxy.Close()
    }

    // 清理统计数据
    statistic.DefaultManager.Close()
}
```

这种分层架构设计确保了系统的可扩展性、可维护性和高性能，同时提供了灵活的配置和管理能力。通过事件驱动的架构和完善的资源管理策略，Mihomo能够高效处理大量并发连接，并保持系统的稳定性和可靠性。
